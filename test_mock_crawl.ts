#!/usr/bin/env -S deno run --allow-net --allow-read --allow-write --allow-env --unstable-kv

// Test crawl pipeline with mocked Firecrawl responses
import { createServicesWithFirecrawl } from "./src/server/dependencies.ts";
import { getWebSources } from "./src/data/web-sources.ts";

async function testMockCrawlPipeline() {
  console.log("🧪 Testing crawl pipeline with mock Firecrawl service...");

  try {
    // Create services with mock Firecrawl
    const services = await createServicesWithFirecrawl({
      firecrawlApiKey: "mock-key",
      mockMode: true
    });

    if (!services.crawling) {
      console.error("❌ Crawling service not available");
      return;
    }

    console.log("✅ Services initialized with mock Firecrawl");

    // Get active web sources
    const webSources = getWebSources({ active: true });
    console.log(`📋 Found ${webSources.length} active web sources`);

    // Test with the first web source
    const testSource = webSources[0];
    console.log(`🎯 Testing with: ${testSource.name} (${testSource.url})`);

    // Start a crawl job
    console.log("🚀 Starting crawl job...");
    const jobResult = await services.crawling.startCrawlJob(testSource);

    if (!jobResult.success) {
      console.error("❌ Failed to start crawl job:", jobResult.error.message);
      return;
    }

    const job = jobResult.data;
    console.log(`✅ Crawl job started: ${job.id}`);
    console.log(`   Firecrawl Job ID: ${job.firecrawlJobId}`);
    console.log(`   Status: ${job.status}`);

    // Monitor job status
    console.log("⏳ Monitoring job status...");
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second

      const statusResult = await services.crawling.getJobStatus(job.id);

      if (statusResult.success) {
        const currentJob = statusResult.data;
        console.log(`📊 Job ${currentJob.id}: ${currentJob.status} (pages: ${currentJob.pagesFound})`);

        if (currentJob.status === "completed") {
          console.log("🎉 Job completed successfully!");

          // Test job processing
          console.log("🔄 Processing completed job...");
          const processResult = await services.crawling.processCompletedJob(job.id);

          if (processResult.success) {
            const result = processResult.data;
            console.log("✅ Job processed successfully!");
            console.log(`   Regulations extracted: ${result.regulations.length}`);
            console.log(`   Changes detected: ${result.changes.length}`);

            // Show extracted regulations
            if (result.regulations.length > 0) {
              console.log("📄 Extracted regulations:");
              result.regulations.forEach((reg, i) => {
                console.log(`   ${i + 1}. ${reg.title.en}`);
                console.log(`      Category: ${reg.category}`);
                console.log(`      Agency: ${reg.source_agency}`);
                console.log(`      Effective: ${reg.timeline.effective_date.toISOString().split('T')[0]}`);
              });
            }

            // Test job processor
            if (services.jobProcessor) {
              console.log("🔄 Testing job processor...");
              const processorResult = await services.jobProcessor.processAllCompletedJobs();
              if (processorResult.success) {
                console.log(`✅ Job processor completed: processed ${processorResult.data.processed}, failed ${processorResult.data.failed}`);
              }
            }
          } else {
            console.error("❌ Failed to process completed job:", processResult.error.message);
          }

          break;
        } else if (currentJob.status === "failed") {
          console.error("❌ Job failed");
          break;
        }
      } else {
        console.error("❌ Failed to get job status:", statusResult.error.message);
        break;
      }

      attempts++;
    }

    if (attempts >= maxAttempts) {
      console.log("⏰ Job monitoring timed out");
    }

    console.log("\n🎉 Mock crawl pipeline test completed!");

  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

if (import.meta.main) {
  await testMockCrawlPipeline();
}
