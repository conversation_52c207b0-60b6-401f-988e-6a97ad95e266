# TypeScript Development Guidelines - WTO Trade Compliance Monitor

## Core Philosophy

This project emphasizes functional programming patterns, type safety, and minimalist architecture. Follow these guidelines to maintain consistency and quality across the codebase.

## Key Principles

- **Functional Programming First**: Prefer pure functions, immutability, and composition over classes
- **Strict Type Safety**: No `any` types - use `unknown` when type is truly unknown
- **Pattern Matching**: Use ts-pattern for complex conditional logic
- **Result Types**: Explicit error handling with Result/Option patterns
- **Minimal Dependencies**: Leverage Deno's standard library and carefully selected external modules

## Project Structure

```
wto/
  deno.json           # Configuration and imports
  server.ts           # Main entry point
  src/
    server/           # HTTP server and API routes
    services/         # Business logic and data processing
    types/           # Type definitions and schemas
    validation/      # Input validation and rules
    ui/             # Frontend components and assets
    tests/          # Test files
  docs/             # Documentation
  static/           # Static assets
```

## TypeScript Configuration

The project uses strict TypeScript settings in `deno.json`:

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true
  }
}
```

## Functional Programming Patterns

### Type Definitions

```typescript
// Use type aliases over interfaces
type RegulationChange = {
  readonly id: string;
  readonly country: string;
  readonly title: string;
  readonly category: "tariff" | "customs" | "trade_agreement" | "compliance";
  readonly impact: "high" | "medium" | "low";
  readonly effectiveDate: Date;
  readonly content: string;
};

// Discriminated unions for state management
type ProcessingState<T> = 
  | { status: "idle" }
  | { status: "processing" }
  | { status: "completed"; data: T }
  | { status: "failed"; error: Error };

// Result types for error handling
type Result<T, E = Error> =
  | { success: true; data: T }
  | { success: false; error: E };
```

### Pure Functions and Immutability

```typescript
// ✅ Pure function - no side effects
const categorizeChange = (change: RegulationChange): string => {
  return match(change.category)
    .with("tariff", () => "Trade Barriers")
    .with("customs", () => "Customs Procedures")
    .with("trade_agreement", () => "International Agreements")
    .with("compliance", () => "Regulatory Compliance")
    .exhaustive();
};

// ✅ Immutable updates
const updateChangeStatus = (
  change: RegulationChange,
  newStatus: string
): RegulationChange => ({
  ...change,
  status: newStatus,
});

// ❌ Avoid mutation
const badUpdate = (change: RegulationChange) => {
  change.status = "processed"; // Don't mutate!
  return change;
};
```

### Pattern Matching with ts-pattern

```typescript
import { match, P } from "ts-pattern";

// Use for complex conditional logic
const handleApiRequest = (req: Request): Promise<Response> => {
  const { pathname, method } = new URL(req.url);
  
  return match({ method, pathname })
    .with({ method: "GET", pathname: "/api/regulations" }, () => 
      handleGetRegulations(req)
    )
    .with({ method: "POST", pathname: "/api/regulations" }, () =>
      handleCreateRegulation(req)
    )
    .with({ pathname: P.string.startsWith("/api/countries/") }, () =>
      handleCountrySpecificRequest(req)
    )
    .otherwise(() => 
      Promise.resolve(new Response("Not Found", { status: 404 }))
    );
};

// State transitions with pattern matching
const processChange = (state: ProcessingState<RegulationChange>) => {
  return match(state)
    .with({ status: "idle" }, () => ({ status: "processing" as const }))
    .with({ status: "processing" }, () => state) // No change
    .with({ status: "completed" }, () => state) // Already done
    .with({ status: "failed" }, () => ({ status: "idle" as const })) // Retry
    .exhaustive();
};
```

### Result Type Error Handling

```typescript
// Function that can fail
const fetchRegulationData = async (url: string): Promise<Result<RegulationChange[]>> => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      return { 
        success: false, 
        error: new Error(`HTTP ${response.status}: ${response.statusText}`) 
      };
    }
    
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error : new Error(String(error))
    };
  }
};

// Using Result types
const processRegulations = async () => {
  const result = await fetchRegulationData("/api/regulations");
  
  return match(result)
    .with({ success: true }, ({ data }) => {
      console.log(`Processing ${data.length} regulations`);
      return data.map(processRegulation);
    })
    .with({ success: false }, ({ error }) => {
      console.error("Failed to fetch regulations:", error.message);
      return [];
    })
    .exhaustive();
};
```

## Server Architecture

### Deno.serve with Functional Routing

```typescript
// src/server/router.ts
import { match } from "ts-pattern";
import { getMimeType } from "@std/media-types";

export const createRouter = (routes: RouteHandler[]) => {
  return async (req: Request): Promise<Response> => {
    const url = new URL(req.url);
    
    // Serve static files
    if (url.pathname.startsWith("/static/")) {
      return await serveStaticFile(url.pathname);
    }
    
    // Match routes
    for (const handler of routes) {
      const result = await handler(req);
      if (result) return result;
    }
    
    return new Response("Not Found", { status: 404 });
  };
};

// Route handler type
type RouteHandler = (req: Request) => Promise<Response | null>;

// Example route handlers
export const regulationRoutes: RouteHandler = async (req) => {
  const { pathname, method } = new URL(req.url);
  
  if (!pathname.startsWith("/api/regulations")) return null;
  
  return match({ method, pathname })
    .with({ method: "GET", pathname: "/api/regulations" }, () =>
      handleGetRegulations(req)
    )
    .with({ method: "POST", pathname: "/api/regulations" }, () =>
      handleCreateRegulation(req)
    )
    .otherwise(() => null);
};
```

### Service Layer with Dependency Injection

```typescript
// src/services/regulation-service.ts
export type RegulationService = {
  readonly findAll: () => Promise<Result<RegulationChange[]>>;
  readonly findByCountry: (country: string) => Promise<Result<RegulationChange[]>>;
  readonly create: (change: Omit<RegulationChange, "id">) => Promise<Result<RegulationChange>>;
};

export const createRegulationService = (
  deps: {
    dataStore: DataStore;
    validator: Validator;
    logger: Logger;
  }
): RegulationService => ({
  findAll: async () => {
    try {
      const regulations = await deps.dataStore.getAll();
      deps.logger.info(`Retrieved ${regulations.length} regulations`);
      return { success: true, data: regulations };
    } catch (error) {
      deps.logger.error("Failed to retrieve regulations", error);
      return { success: false, error: error as Error };
    }
  },
  
  findByCountry: async (country: string) => {
    const validation = deps.validator.validateCountry(country);
    if (!validation.success) {
      return { success: false, error: validation.error };
    }
    
    try {
      const regulations = await deps.dataStore.getByCountry(country);
      return { success: true, data: regulations };
    } catch (error) {
      return { success: false, error: error as Error };
    }
  },
  
  create: async (changeData) => {
    const validation = deps.validator.validateRegulationChange(changeData);
    if (!validation.success) {
      return { success: false, error: validation.error };
    }
    
    const change: RegulationChange = {
      ...changeData,
      id: crypto.randomUUID(),
    };
    
    try {
      await deps.dataStore.save(change);
      deps.logger.info(`Created regulation ${change.id}`);
      return { success: true, data: change };
    } catch (error) {
      return { success: false, error: error as Error };
    }
  },
});
```

## Testing Strategies

### Behavior-Driven Testing

```typescript
// src/tests/regulation-service.test.ts
import { assertEquals, assertExists } from "@std/assert";
import { createRegulationService } from "../services/regulation-service.ts";

// Test through public API only
Deno.test("RegulationService - findAll returns all regulations", async () => {
  // Arrange
  const mockDataStore = {
    getAll: () => Promise.resolve([
      { id: "1", country: "US", title: "Test Regulation", category: "tariff" as const },
    ]),
    getByCountry: () => Promise.resolve([]),
    save: () => Promise.resolve(),
  };
  
  const service = createRegulationService({
    dataStore: mockDataStore,
    validator: createMockValidator(),
    logger: createMockLogger(),
  });
  
  // Act
  const result = await service.findAll();
  
  // Assert
  assertEquals(result.success, true);
  if (result.success) {
    assertEquals(result.data.length, 1);
    assertEquals(result.data[0].country, "US");
  }
});

// Integration test with HTTP
Deno.test("API - GET /api/regulations returns regulation list", async () => {
  const server = createTestServer();
  const response = await fetch(`${server.url}/api/regulations`);
  
  assertEquals(response.status, 200);
  const data = await response.json();
  assertExists(data);
  
  await server.close();
});
```

### Mock Service Worker for API Testing

```typescript
// src/tests/mocks/handlers.ts
import { http, HttpResponse } from "msw";

export const handlers = [
  http.get("/api/regulations", () => {
    return HttpResponse.json([
      {
        id: "1",
        country: "US",
        title: "New Tariff on Steel",
        category: "tariff",
        impact: "high",
        effectiveDate: "2024-01-01",
        content: "Updated steel tariff rates...",
      },
    ]);
  }),
  
  http.post("/api/regulations", async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json(
      { ...body, id: crypto.randomUUID() },
      { status: 201 }
    );
  }),
];
```

## Data Processing Patterns

### Functional Pipeline Processing

```typescript
// src/services/data-processing.ts
import { pipe } from "./utils/functional.ts";

type ProcessingPipeline<T, R> = (input: T) => Promise<Result<R>>;

const createProcessingPipeline = <T, R>(
  ...steps: Array<(input: any) => Promise<Result<any>>>
): ProcessingPipeline<T, R> => {
  return async (input: T) => {
    let current: any = input;
    
    for (const step of steps) {
      const result = await step(current);
      if (!result.success) {
        return result;
      }
      current = result.data;
    }
    
    return { success: true, data: current };
  };
};

// Usage
const processRegulationData = createProcessingPipeline(
  validateInput,
  normalizeData,
  categorizeChanges,
  detectImpact,
  generateAlerts
);

const result = await processRegulationData(rawData);
```

### Immutable State Updates

```typescript
// src/services/state-management.ts
type AppState = {
  readonly regulations: readonly RegulationChange[];
  readonly notifications: readonly Notification[];
  readonly processingStatus: ProcessingState<unknown>;
};

type StateUpdate<T = unknown> = (state: AppState) => AppState;

const addRegulation = (regulation: RegulationChange): StateUpdate => (state) => ({
  ...state,
  regulations: [...state.regulations, regulation],
});

const updateProcessingStatus = <T>(status: ProcessingState<T>): StateUpdate => (state) => ({
  ...state,
  processingStatus: status,
});

// State reducer with pattern matching
const reduceState = (state: AppState, action: Action): AppState => {
  return match(action)
    .with({ type: "ADD_REGULATION" }, ({ payload }) => 
      addRegulation(payload)(state)
    )
    .with({ type: "UPDATE_STATUS" }, ({ payload }) =>
      updateProcessingStatus(payload)(state)
    )
    .with({ type: "CLEAR_NOTIFICATIONS" }, () => ({
      ...state,
      notifications: [],
    }))
    .exhaustive();
};
```

## Development Workflow

### 1. Type-First Development
- Define types and interfaces before implementation
- Use discriminated unions for complex state
- Leverage TypeScript's type inference

### 2. Pure Function Implementation
- Write pure functions without side effects
- Use function composition for complex operations
- Test functions in isolation

### 3. Pattern Matching for Logic
- Replace complex if/else chains with pattern matching
- Use exhaustive matching for state transitions
- Leverage pattern guards for complex conditions

### 4. Result-Based Error Handling
- Return Result types instead of throwing exceptions
- Use pattern matching to handle success/failure cases
- Maintain error context throughout the call stack

### 5. Testing Strategy
- Test behavior through public APIs
- Use dependency injection for testability
- Mock external dependencies with MSW or test doubles

## Code Style Guidelines

### Formatting
- Use 2 spaces for indentation
- Semicolons required
- Double quotes for strings
- Line width: 80 characters

### Naming Conventions
- `camelCase` for variables and functions
- `PascalCase` for types and interfaces
- `UPPER_SNAKE_CASE` for constants
- Descriptive names over brevity

### Import Organization
```typescript
// 1. Standard library imports
import { assertEquals } from "@std/assert";
import { serve } from "@std/http";

// 2. Third-party imports
import { match } from "ts-pattern";

// 3. Local imports
import { RegulationChange } from "../types/regulation.ts";
import { createService } from "./service.ts";
```

### Comments and Documentation
- Use TSDoc for public APIs
- Explain complex business logic
- Document type constraints and assumptions

```typescript
/**
 * Processes regulation changes and generates appropriate notifications.
 * 
 * @param changes - Array of regulation changes to process
 * @param options - Processing options including notification preferences
 * @returns Promise resolving to processing result with generated notifications
 * 
 * @example
 * ```typescript
 * const result = await processRegulationChanges(changes, {
 *   notifyImmediately: true,
 *   categories: ["tariff", "customs"]
 * });
 * ```
 */
export const processRegulationChanges = async (
  changes: readonly RegulationChange[],
  options: ProcessingOptions
): Promise<Result<ProcessingResult>> => {
  // Implementation...
};
```

## Performance Considerations

### Async Operations
- Use `Promise.all()` for concurrent operations
- Implement proper cancellation with `AbortController`
- Stream large datasets instead of loading in memory

### Memory Management
- Prefer readonly arrays and objects
- Use structural sharing for large state updates
- Implement proper cleanup for event listeners

### Caching Strategy
```typescript
// Simple in-memory cache with TTL
export const createCache = <K, V>(ttlMs: number) => {
  const cache = new Map<K, { value: V; expires: number }>();
  
  return {
    get: (key: K): V | undefined => {
      const entry = cache.get(key);
      if (!entry || Date.now() > entry.expires) {
        cache.delete(key);
        return undefined;
      }
      return entry.value;
    },
    
    set: (key: K, value: V): void => {
      cache.set(key, {
        value,
        expires: Date.now() + ttlMs,
      });
    },
    
    clear: (): void => cache.clear(),
  };
};
```

## Security Best Practices

### Input Validation
```typescript
// src/validation/schemas.ts
import { z } from "zod";

export const RegulationChangeSchema = z.object({
  country: z.string().min(2).max(3), // ISO country codes
  title: z.string().min(1).max(200),
  category: z.enum(["tariff", "customs", "trade_agreement", "compliance"]),
  impact: z.enum(["high", "medium", "low"]),
  effectiveDate: z.coerce.date(),
  content: z.string().min(10),
});

export const validateRegulationChange = (data: unknown): Result<RegulationChange> => {
  const result = RegulationChangeSchema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  } else {
    return { 
      success: false, 
      error: new Error(`Validation failed: ${result.error.message}`) 
    };
  }
};
```

### Safe Environment Configuration
```typescript
// src/config/environment.ts
const getRequiredEnv = (key: string): string => {
  const value = Deno.env.get(key);
  if (!value) {
    throw new Error(`Required environment variable ${key} is not set`);
  }
  return value;
};

export const config = {
  port: parseInt(Deno.env.get("PORT") ?? "8000"),
  apiUrl: getRequiredEnv("API_URL"),
  dbUrl: getRequiredEnv("DATABASE_URL"),
  logLevel: Deno.env.get("LOG_LEVEL") ?? "info",
} as const;
```

## Conclusion

This guide establishes a foundation for building maintainable, type-safe TypeScript applications with functional programming principles. Focus on immutability, explicit error handling, and behavior-driven testing to create robust systems that are easy to understand, test, and extend.

Remember:
- Types are your documentation and safety net
- Pure functions are easier to test and reason about
- Pattern matching makes complex logic more readable
- Result types make error handling explicit and composable
- Test behavior, not implementation details

For questions or clarifications on these guidelines, refer to the existing codebase examples or create issues for discussion.