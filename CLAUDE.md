# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
```bash
# Start development server with hot reload
deno task dev

# Run production server
deno task start

# Type check without running
deno task typecheck

# Format code
deno task fmt

# Lint code  
deno task lint
```

### Testing
```bash
# Run all tests
deno task test

# Run specific test file
deno test src/tests/core_test.ts --allow-all

# Run specific test with pattern
deno test --filter "Result type" --allow-all

# Run tests without type checking (for debugging)
deno test --no-check --allow-all
```

## Architecture Overview

This is a **functional TypeScript** application built on Deno that demonstrates modern FP patterns for a WTO trade compliance monitoring system.

### Core Architectural Principles

1. **Zero Classes**: Everything is built with pure functions, never classes
2. **Algebraic Data Types**: Heavy use of `Result<T, E>` and `Option<T>` for error handling
3. **Pattern Matching**: All conditional logic uses `ts-pattern` with exhaustive matching
4. **Immutable Data**: All types are `readonly`, updates create new objects
5. **Dependency Injection**: Pure functional services composed via factory functions

### Key Type System

**Result Type**: The backbone of error handling throughout the system
```typescript
type Result<T, E = Error> =
  | { readonly success: true; readonly data: T }
  | { readonly success: false; readonly error: E };
```

**Service Pattern**: All services return Result types and are created via pure factories
```typescript
type RegulationService = {
  readonly findAll: (filters?: RegulationFilters) => Promise<Result<readonly Regulation[]>>;
  readonly create: (regulation: RegulationInput) => Promise<Result<Regulation>>;
};
```

### Router Architecture

The application uses a **functional router** (`src/lib/router.ts`) that:
- Uses pattern matching for route resolution
- Supports regex patterns with parameter extraction
- Handles CORS and static file serving
- Returns `RouteConfig` objects composed into a router function

Routes are defined in `src/server/routes.ts` using pure functions that return `RouteConfig` objects.

### Dependency Injection

Services are created via pure factory functions in `src/server/dependencies.ts`:
- All services are interfaces with `readonly` methods
- Implementation is provided by factory functions that take dependencies
- Main `Services` type composes all service interfaces
- `createServices()` function wires everything together

### Validation System

Uses **pure TypeScript validation functions** (`src/validation/schemas.ts`):
- Each validator is a pure function returning `Result<T, ValidationError>`
- Validation integrates with API request processing
- Input sanitization functions for security
- Type-safe validation without external schema libraries

### Testing Philosophy

**Behavior-Driven Testing**: Tests describe behavior through Given-When-Then patterns:
- Test through public APIs only, not implementation details
- Use descriptive test names like "API should return regulations list when requested"
- Mock dependencies via dependency injection, not implementation details

### File Organization

```
src/
├── lib/router.ts              # Functional router with pattern matching
├── types/                     # Algebraic data types
│   ├── core.ts               # Result<T,E>, Option<T>, pipeline types
│   ├── regulation.ts         # Domain types for regulations
│   └── change.ts             # Domain types for change detection
├── server/                    # HTTP layer
│   ├── dependencies.ts       # DI container with service factories
│   ├── routes.ts             # Route handlers using pattern matching
│   └── templates.ts          # HTMX response templates
├── services/                  # Business logic (pure functions)
├── validation/                # Pure TypeScript validation functions
└── tests/                     # Behavior-driven tests
```

## Development Patterns

### Adding New Routes
1. Define route handler in `src/server/routes.ts` 
2. Use pattern matching with `match(result)` for Result handling
3. Return `RouteConfig` from route factory functions
4. Add to routes array in `createApiRoutes()`

### Adding New Services  
1. Define service interface in `src/server/dependencies.ts`
2. Create factory function that returns the interface
3. All service methods must return `Promise<Result<T>>`
4. Wire into main `Services` type and `createServices()` function

### Error Handling
- Never throw exceptions, always return `Result<T, E>` 
- Use `match(result)` pattern matching to handle success/error cases
- Use `isOk(result)` and `isErr(result)` type guards when needed
- Chain operations with `flatMapResult` for monadic composition

### Validation
- Create pure TypeScript validation functions
- All validators return `Result<T, ValidationError>`
- Use `validateApiRequest()` for HTTP request validation
- Sanitize inputs with provided sanitization functions

### Testing
- Write behavior-driven tests with Given-When-Then structure
- Test through service interfaces, not implementations
- Use dependency injection for mocking
- Focus on API behavior, not internal logic
- Run tests with appropriate permissions: `--allow-net --allow-read --allow-write --allow-env`

## Key Dependencies

- **ts-pattern**: Pattern matching for all conditional logic
- **@std/**: Deno standard library modules
- **HTMX**: Server-side rendering with dynamic updates

## TypeScript Configuration

The project uses **strict TypeScript** settings:
- `noImplicitAny`, `exactOptionalPropertyTypes`, `noUncheckedIndexedAccess`
- Zero tolerance for `any` types - use `unknown` instead
- All compiler options enforce functional programming patterns
- Property access from index signatures requires bracket notation