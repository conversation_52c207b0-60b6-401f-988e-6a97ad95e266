{"version": "5", "specifiers": {"jsr:@std/assert@1": "1.0.13", "jsr:@std/cli@^1.0.12": "1.0.14", "jsr:@std/dotenv@0.225": "0.225.2", "jsr:@std/encoding@^1.0.7": "1.0.7", "jsr:@std/fmt@^1.0.5": "1.0.6", "jsr:@std/html@^1.0.3": "1.0.3", "jsr:@std/http@1": "1.0.13", "jsr:@std/internal@^1.0.6": "1.0.6", "jsr:@std/media-types@1": "1.1.0", "jsr:@std/media-types@^1.1.0": "1.1.0", "jsr:@std/net@^1.0.4": "1.0.4", "jsr:@std/path@1": "1.0.8", "jsr:@std/path@^1.0.8": "1.0.8", "jsr:@std/streams@^1.0.9": "1.0.9"}, "jsr": {"@std/assert@1.0.13": {"integrity": "ae0d31e41919b12c656c742b22522c32fb26ed0cba32975cb0de2a273cb68b29", "dependencies": ["jsr:@std/internal"]}, "@std/cli@1.0.14": {"integrity": "b09ee9921cd476c0e08185ed2bfce682d45ecf4654f26c31b4aa244a2c5c024e"}, "@std/dotenv@0.225.2": {"integrity": "e2025dce4de6c7bca21dece8baddd4262b09d5187217e231b033e088e0c4dd23"}, "@std/encoding@1.0.7": {"integrity": "f631247c1698fef289f2de9e2a33d571e46133b38d042905e3eac3715030a82d"}, "@std/fmt@1.0.6": {"integrity": "a2c56a69a2369876ddb3ad6a500bb6501b5bad47bb3ea16bfb0c18974d2661fc"}, "@std/html@1.0.3": {"integrity": "7a0ac35e050431fb49d44e61c8b8aac1ebd55937e0dc9ec6409aa4bab39a7988"}, "@std/http@1.0.13": {"integrity": "d29618b982f7ae44380111f7e5b43da59b15db64101198bb5f77100d44eb1e1e", "dependencies": ["jsr:@std/cli", "jsr:@std/encoding", "jsr:@std/fmt", "jsr:@std/html", "jsr:@std/media-types@^1.1.0", "jsr:@std/net", "jsr:@std/path@^1.0.8", "jsr:@std/streams"]}, "@std/internal@1.0.6": {"integrity": "9533b128f230f73bd209408bb07a4b12f8d4255ab2a4d22a1fd6d87304aca9a4"}, "@std/media-types@1.1.0": {"integrity": "c9d093f0c05c3512932b330e3cc1fe1d627b301db33a4c2c2185c02471d6eaa4"}, "@std/net@1.0.4": {"integrity": "2f403b455ebbccf83d8a027d29c5a9e3a2452fea39bb2da7f2c04af09c8bc852"}, "@std/path@1.0.8": {"integrity": "548fa456bb6a04d3c1a1e7477986b6cffbce95102d0bb447c67c4ee70e0364be"}, "@std/streams@1.0.9": {"integrity": "a9d26b1988cdd7aa7b1f4b51e1c36c1557f3f252880fa6cc5b9f37078b1a5035"}}, "remote": {"https://esm.sh/ts-pattern@5.0.5": "5faa230676c0eaf57a84859d3d8c5d3f384e268fc57dd0fc1201abdf746e040d", "https://esm.sh/ts-pattern@5.0.5/denonext/ts-pattern.mjs": "e80422b4576036eb0f6bd5a00bb000a4ab15ba8898955278cd7490dec5b6c857"}, "workspace": {"dependencies": ["jsr:@std/assert@1", "jsr:@std/crypto@1", "jsr:@std/datetime@1", "jsr:@std/dotenv@0.225", "jsr:@std/fs@1", "jsr:@std/http@1", "jsr:@std/media-types@1", "jsr:@std/path@1", "jsr:@std/testing@1"]}}