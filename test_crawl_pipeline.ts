#!/usr/bin/env -S deno run --allow-net --allow-read --allow-write --allow-env --unstable-kv

// Comprehensive test to diagnose crawl job completion issues
import { createServicesWithFirecrawl } from "./src/server/dependencies.ts";
import { getWebSources } from "./src/data/web-sources.ts";

async function testCrawlPipeline() {
  console.log("🔍 Testing crawl job pipeline...");
  
  // Check if Firecrawl API key is available
  const firecrawlApiKey = Deno.env.get("FIRECRAWL_API_KEY");
  if (!firecrawlApiKey) {
    console.error("❌ FIRECRAWL_API_KEY environment variable not set");
    console.log("   Please set FIRECRAWL_API_KEY to test the crawl pipeline");
    return;
  }
  
  console.log("✅ Firecrawl API key found");
  
  try {
    // Create services
    const services = await createServicesWithFirecrawl({ firecrawlApiKey });
    
    if (!services.crawling) {
      console.error("❌ Crawling service not available");
      return;
    }
    
    console.log("✅ Services initialized");
    
    // Get active web sources
    const webSources = getWebSources({ active: true });
    console.log(`📋 Found ${webSources.length} active web sources`);
    
    if (webSources.length === 0) {
      console.error("❌ No active web sources found");
      return;
    }
    
    // Test with the first web source
    const testSource = webSources[0];
    console.log(`🎯 Testing with: ${testSource.name} (${testSource.url})`);
    
    // Start a crawl job
    console.log("🚀 Starting crawl job...");
    const jobResult = await services.crawling.startCrawlJob(testSource);
    
    if (!jobResult.success) {
      console.error("❌ Failed to start crawl job:", jobResult.error.message);
      return;
    }
    
    const job = jobResult.data;
    console.log(`✅ Crawl job started: ${job.id}`);
    console.log(`   Firecrawl Job ID: ${job.firecrawlJobId}`);
    console.log(`   Status: ${job.status}`);
    
    // Monitor job status for up to 5 minutes
    console.log("⏳ Monitoring job status...");
    const maxWaitTime = 5 * 60 * 1000; // 5 minutes
    const checkInterval = 10 * 1000; // 10 seconds
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const statusResult = await services.crawling.getJobStatus(job.id);
      
      if (statusResult.success) {
        const currentJob = statusResult.data;
        console.log(`📊 Job ${currentJob.id}: ${currentJob.status} (pages: ${currentJob.pagesFound})`);
        
        if (currentJob.status === "completed") {
          console.log("🎉 Job completed successfully!");
          
          // Test job processing
          console.log("🔄 Processing completed job...");
          const processResult = await services.crawling.processCompletedJob(job.id);
          
          if (processResult.success) {
            const result = processResult.data;
            console.log("✅ Job processed successfully!");
            console.log(`   Regulations extracted: ${result.regulations.length}`);
            console.log(`   Changes detected: ${result.changes.length}`);
            
            // Show some sample data
            if (result.regulations.length > 0) {
              console.log("📄 Sample regulations:");
              result.regulations.slice(0, 3).forEach((reg, i) => {
                console.log(`   ${i + 1}. ${reg.title} (confidence: ${reg.confidence})`);
              });
            }
          } else {
            console.error("❌ Failed to process completed job:", processResult.error.message);
          }
          
          break;
        } else if (currentJob.status === "failed") {
          console.error("❌ Job failed");
          break;
        }
      } else {
        console.error("❌ Failed to get job status:", statusResult.error.message);
        break;
      }
      
      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }
    
    if (Date.now() - startTime >= maxWaitTime) {
      console.log("⏰ Job monitoring timed out after 5 minutes");
      
      // Check final status
      const finalStatusResult = await services.crawling.getJobStatus(job.id);
      if (finalStatusResult.success) {
        console.log(`   Final status: ${finalStatusResult.data.status}`);
      }
    }
    
    // Test direct Firecrawl API call
    if (services.firecrawl) {
      console.log("\n🔥 Testing direct Firecrawl API...");
      const directResult = await services.firecrawl.scrapeUrl(testSource.url, {
        onlyMainContent: true,
        includeHtml: false,
      });
      
      if (directResult.success) {
        console.log("✅ Direct Firecrawl scrape successful");
        console.log(`   Title: ${directResult.data.metadata.title}`);
        console.log(`   Content length: ${directResult.data.markdown.length} chars`);
      } else {
        console.error("❌ Direct Firecrawl scrape failed:", directResult.error.message);
      }
    }
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

if (import.meta.main) {
  await testCrawlPipeline();
}
