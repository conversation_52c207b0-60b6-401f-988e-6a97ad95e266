// Environment configuration for WTO Compliance Monitor
import type { FirecrawlConfig } from "../services/firecrawl.ts";

export type EnvironmentConfig = {
  readonly port: number;
  readonly environment: "development" | "production" | "test";
  readonly firecrawl: FirecrawlConfig;
  readonly database?: {
    readonly url: string;
    readonly maxConnections: number;
  };
  readonly logging: {
    readonly level: "debug" | "info" | "warn" | "error";
    readonly enableConsole: boolean;
  };
};

// Load configuration from environment variables
export const loadEnvironmentConfig = (): EnvironmentConfig => {
  const port = parseInt(Deno.env.get("PORT") || "8000");
  const environment = (Deno.env.get("NODE_ENV") || "development") as EnvironmentConfig["environment"];
  
  // Firecrawl configuration
  const firecrawlApiKey = Deno.env.get("FIRECRAWL_API_KEY");
  if (!firecrawlApiKey) {
    throw new Error("FIRECRAWL_API_KEY environment variable is required");
  }

  const config: EnvironmentConfig = {
    port,
    environment,
    firecrawl: {
      apiKey: firecrawlApiKey,
      baseUrl: Deno.env.get("FIRECRAWL_BASE_URL") || "https://api.firecrawl.dev",
    },
    logging: {
      level: (Deno.env.get("LOG_LEVEL") || "info") as EnvironmentConfig["logging"]["level"],
      enableConsole: Deno.env.get("LOG_CONSOLE") !== "false",
    },
  };

  // Optional database configuration
  const databaseUrl = Deno.env.get("DATABASE_URL");
  if (databaseUrl) {
    config.database = {
      url: databaseUrl,
      maxConnections: parseInt(Deno.env.get("DATABASE_MAX_CONNECTIONS") || "10"),
    };
  }

  return config;
};

// Validate configuration
export const validateConfig = (config: EnvironmentConfig): Result<void> => {
  if (!config.firecrawl.apiKey) {
    return Err(new Error("Firecrawl API key is required"));
  }

  if (config.port < 1 || config.port > 65535) {
    return Err(new Error("Port must be between 1 and 65535"));
  }

  return Ok(undefined);
};

// Development/example configuration (never use in production)
export const getExampleConfig = (): Partial<EnvironmentConfig> => ({
  port: 8000,
  environment: "development",
  firecrawl: {
    apiKey: "your-firecrawl-api-key-here",
    baseUrl: "https://api.firecrawl.dev",
  },
  logging: {
    level: "debug",
    enableConsole: true,
  },
});

// Helper to create .env file template
export const createEnvTemplate = (): string => {
  return `# WTO Compliance Monitor Environment Configuration

# Server Configuration
PORT=8000
NODE_ENV=development

# Firecrawl Configuration (Required)
FIRECRAWL_API_KEY=your-firecrawl-api-key-here
FIRECRAWL_BASE_URL=https://api.firecrawl.dev

# Database Configuration (Optional)
# DATABASE_URL=postgres://user:password@localhost/wto_compliance
# DATABASE_MAX_CONNECTIONS=10

# Logging Configuration
LOG_LEVEL=info
LOG_CONSOLE=true
`;
};

import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";