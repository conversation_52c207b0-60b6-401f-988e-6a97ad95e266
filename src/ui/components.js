// Web Components for WTO Compliance Monitor

// Country Coverage Component
class CountryCoverageComponent extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
    }

    connectedCallback() {
        this.loadCountryData();
    }

    async loadCountryData() {
        try {
            const response = await fetch('/api/dashboard/stats');
            const data = await response.json();
            
            if (data.success) {
                this.render(data.data.distributions.byCountry);
            } else {
                this.renderError('Failed to load country data');
            }
        } catch (error) {
            this.renderError('Network error loading countries');
        }
    }

    render(countryData) {
        const countries = [
            { code: 'US', name: 'United States', flag: '🇺🇸' },
            { code: 'EU', name: 'European Union', flag: '🇪🇺' },
            { code: 'JP', name: 'Japan', flag: '🇯🇵' },
            { code: 'CN', name: 'China', flag: '🇨🇳' },
            { code: 'GB', name: 'United Kingdom', flag: '🇬🇧' }
        ];

        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                    background: white;
                    border-radius: 8px;
                    padding: 1.5rem;
                    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                    border: 1px solid #e2e8f0;
                }
                
                .countries-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                }
                
                .country-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 1rem;
                    border: 1px solid #e2e8f0;
                    border-radius: 8px;
                    transition: all 0.2s;
                    cursor: pointer;
                }
                
                .country-item:hover {
                    background: #f8fafc;
                    border-color: #2563eb;
                    transform: translateY(-1px);
                }
                
                .country-flag {
                    font-size: 1.5rem;
                }
                
                .country-info {
                    flex: 1;
                }
                
                .country-name {
                    font-weight: 600;
                    color: #1e293b;
                    font-size: 0.875rem;
                    margin-bottom: 0.25rem;
                }
                
                .country-count {
                    font-size: 0.75rem;
                    color: #64748b;
                }
                
                .country-badge {
                    background: #2563eb;
                    color: white;
                    padding: 0.25rem 0.5rem;
                    border-radius: 9999px;
                    font-size: 0.75rem;
                    font-weight: 600;
                }
                
                .error {
                    color: #dc2626;
                    text-align: center;
                    padding: 2rem;
                }
            </style>
            <div class="countries-grid">
                ${countries.map(country => {
                    const count = countryData[country.code] || 0;
                    return `
                        <div class="country-item" onclick="filterByCountry('${country.code}')">
                            <div class="country-flag">${country.flag}</div>
                            <div class="country-info">
                                <div class="country-name">${country.name}</div>
                                <div class="country-count">${count} regulations</div>
                            </div>
                            <div class="country-badge">${count}</div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    renderError(message) {
        this.shadowRoot.innerHTML = `
            <style>
                .error {
                    color: #dc2626;
                    text-align: center;
                    padding: 2rem;
                    background: white;
                    border-radius: 8px;
                    border: 1px solid #fee2e2;
                }
            </style>
            <div class="error">${message}</div>
        `;
    }
}

// Regulation Card Component
class RegulationCardComponent extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
    }

    connectedCallback() {
        this.loadRegulations();
    }

    async loadRegulations() {
        try {
            const response = await fetch('/api/regulations?limit=10');
            const data = await response.json();
            
            if (data.success) {
                this.render(data.data.regulations);
            } else {
                this.renderError('Failed to load regulations');
            }
        } catch (error) {
            this.renderError('Network error loading regulations');
        }
    }

    render(regulations) {
        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                }
                
                .regulation-card {
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    transition: all 0.2s;
                    margin-bottom: 1rem;
                }
                
                .regulation-card:hover {
                    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
                    transform: translateY(-1px);
                }
                
                .regulation-header {
                    padding: 1.5rem;
                    border-bottom: 1px solid #f1f5f9;
                }
                
                .regulation-title {
                    font-size: 1.25rem;
                    font-weight: 700;
                    color: #1e293b;
                    margin-bottom: 0.5rem;
                }
                
                .regulation-meta {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    flex-wrap: wrap;
                }
                
                .badge {
                    padding: 0.25rem 0.75rem;
                    border-radius: 9999px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                }
                
                .badge-country {
                    background: #0891b2;
                    color: white;
                }
                
                .badge-category {
                    background: #e2e8f0;
                    color: #475569;
                }
                
                .regulation-body {
                    padding: 1.5rem;
                }
                
                .regulation-description {
                    color: #64748b;
                    line-height: 1.6;
                    margin-bottom: 1rem;
                }
                
                .regulation-details {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                }
                
                .detail-item {
                    display: flex;
                    flex-direction: column;
                    gap: 0.25rem;
                }
                
                .detail-label {
                    font-size: 0.75rem;
                    font-weight: 600;
                    color: #64748b;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                }
                
                .detail-value {
                    font-weight: 500;
                    color: #1e293b;
                }
                
                .impact-bar {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }
                
                .impact-progress {
                    flex: 1;
                    height: 4px;
                    background: #e2e8f0;
                    border-radius: 2px;
                    overflow: hidden;
                }
                
                .impact-fill {
                    height: 100%;
                    background: linear-gradient(90deg, #059669, #d97706, #dc2626);
                    transition: width 0.3s ease;
                }
                
                .loading {
                    text-align: center;
                    padding: 2rem;
                    color: #64748b;
                }
                
                .error {
                    color: #dc2626;
                    text-align: center;
                    padding: 2rem;
                    background: #fee2e2;
                    border-radius: 8px;
                }
            </style>
            ${regulations.map(reg => `
                <div class="regulation-card">
                    <div class="regulation-header">
                        <div class="regulation-title">${reg.title.en || 'Untitled Regulation'}</div>
                        <div class="regulation-meta">
                            <span class="badge badge-country">${reg.country_code}</span>
                            <span class="badge badge-category">${reg.category.replace('_', ' ')}</span>
                        </div>
                    </div>
                    <div class="regulation-body">
                        <div class="regulation-description">
                            ${reg.description.en || 'No description available'}
                        </div>
                        <div class="regulation-details">
                            <div class="detail-item">
                                <div class="detail-label">Effective Date</div>
                                <div class="detail-value">${new Date(reg.timeline.effective_date).toLocaleDateString()}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Source Agency</div>
                                <div class="detail-value">${reg.source_agency}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">HS Codes</div>
                                <div class="detail-value">${reg.hs_codes.join(', ') || 'None specified'}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Economic Impact</div>
                                <div class="impact-bar">
                                    <div class="impact-progress">
                                        <div class="impact-fill" style="width: ${reg.impact_assessment.economic * 10}%"></div>
                                    </div>
                                    <span>${reg.impact_assessment.economic}/10</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('')}
        `;
    }

    renderError(message) {
        this.shadowRoot.innerHTML = `
            <div class="error">${message}</div>
        `;
    }
}

// Change Item Component
class ChangeItemComponent extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
    }

    connectedCallback() {
        this.loadChanges();
    }

    async loadChanges() {
        try {
            const response = await fetch('/api/changes?limit=20');
            const data = await response.json();
            
            if (data.success) {
                this.render(data.data.changes);
            } else {
                this.renderError('Failed to load changes');
            }
        } catch (error) {
            this.renderError('Network error loading changes');
        }
    }

    render(changes) {
        const priorityColors = {
            critical: '#dc2626',
            high: '#d97706',
            medium: '#0891b2',
            low: '#64748b'
        };

        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                }
                
                .change-item {
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                    transition: all 0.2s;
                    margin-bottom: 1rem;
                }
                
                .change-item:hover {
                    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
                    transform: translateY(-1px);
                }
                
                .change-header {
                    padding: 1.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    border-bottom: 1px solid #f1f5f9;
                }
                
                .change-title {
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: #1e293b;
                }
                
                .priority-badge {
                    padding: 0.25rem 0.75rem;
                    border-radius: 9999px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    color: white;
                }
                
                .change-body {
                    padding: 1.5rem;
                }
                
                .change-summary {
                    color: #64748b;
                    margin-bottom: 1rem;
                    line-height: 1.6;
                }
                
                .change-meta {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    font-size: 0.875rem;
                    color: #64748b;
                    flex-wrap: wrap;
                }
                
                .change-type {
                    font-weight: 600;
                    color: #475569;
                    text-transform: capitalize;
                }
                
                .change-industries {
                    display: flex;
                    gap: 0.5rem;
                    flex-wrap: wrap;
                }
                
                .industry-tag {
                    background: #f1f5f9;
                    color: #475569;
                    padding: 0.25rem 0.5rem;
                    border-radius: 4px;
                    font-size: 0.75rem;
                    font-weight: 500;
                }
                
                .error {
                    color: #dc2626;
                    text-align: center;
                    padding: 2rem;
                    background: #fee2e2;
                    border-radius: 8px;
                }
                
                .no-changes {
                    text-align: center;
                    padding: 3rem;
                    color: #64748b;
                }
                
                .no-changes-icon {
                    font-size: 3rem;
                    margin-bottom: 1rem;
                }
            </style>
            ${changes.length > 0 ? changes.map(change => `
                <div class="change-item">
                    <div class="change-header">
                        <div class="change-title">${change.summary.en || 'Change Detected'}</div>
                        <span class="priority-badge" style="background: ${priorityColors[change.priority]}">${change.priority}</span>
                    </div>
                    <div class="change-body">
                        <div class="change-summary">
                            ${change.detailed_changes.map(detail => detail.change_description).join('; ')}
                        </div>
                        <div class="change-meta">
                            <span class="change-type">${change.change_type.replace('_', ' ')}</span>
                            <span>•</span>
                            <span>Detected: ${new Date(change.detected_at).toLocaleDateString()}</span>
                            <span>•</span>
                            <span>Impact: ${Math.round((change.impact_score.economic + change.impact_score.operational + change.impact_score.compliance + change.impact_score.urgency) / 4)}/10</span>
                        </div>
                        ${change.affected_industries.length > 0 ? `
                            <div class="change-industries" style="margin-top: 1rem;">
                                ${change.affected_industries.map(industry => `
                                    <span class="industry-tag">${industry}</span>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('') : `
                <div class="no-changes">
                    <div class="no-changes-icon">📋</div>
                    <div>No changes detected recently</div>
                </div>
            `}
        `;
    }

    renderError(message) {
        this.shadowRoot.innerHTML = `
            <div class="error">${message}</div>
        `;
    }
}

// Subscription Manager Component
class SubscriptionManagerComponent extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
        this.subscriptions = [];
    }

    connectedCallback() {
        this.render();
    }

    render() {
        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                }
                
                .subscription-list {
                    display: grid;
                    gap: 1rem;
                }
                
                .subscription-card {
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }
                
                .subscription-header {
                    padding: 1.5rem;
                    border-bottom: 1px solid #f1f5f9;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
                
                .subscription-name {
                    font-size: 1.125rem;
                    font-weight: 600;
                    color: #1e293b;
                }
                
                .subscription-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-size: 0.875rem;
                }
                
                .status-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: #059669;
                }
                
                .subscription-body {
                    padding: 1.5rem;
                }
                
                .subscription-details {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                    margin-bottom: 1rem;
                }
                
                .detail-item {
                    display: flex;
                    flex-direction: column;
                    gap: 0.25rem;
                }
                
                .detail-label {
                    font-size: 0.75rem;
                    font-weight: 600;
                    color: #64748b;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                }
                
                .detail-value {
                    font-weight: 500;
                    color: #1e293b;
                }
                
                .tag-list {
                    display: flex;
                    gap: 0.5rem;
                    flex-wrap: wrap;
                }
                
                .tag {
                    background: #f1f5f9;
                    color: #475569;
                    padding: 0.25rem 0.5rem;
                    border-radius: 4px;
                    font-size: 0.75rem;
                    font-weight: 500;
                }
                
                .subscription-actions {
                    display: flex;
                    gap: 0.5rem;
                    justify-content: flex-end;
                    padding-top: 1rem;
                    border-top: 1px solid #f1f5f9;
                }
                
                .btn {
                    padding: 0.5rem 1rem;
                    border-radius: 6px;
                    font-size: 0.875rem;
                    font-weight: 500;
                    cursor: pointer;
                    border: 1px solid;
                    transition: all 0.2s;
                }
                
                .btn-secondary {
                    background: white;
                    color: #475569;
                    border-color: #d1d5db;
                }
                
                .btn-secondary:hover {
                    background: #f9fafb;
                    border-color: #9ca3af;
                }
                
                .btn-danger {
                    background: #dc2626;
                    color: white;
                    border-color: #dc2626;
                }
                
                .btn-danger:hover {
                    background: #b91c1c;
                    border-color: #b91c1c;
                }
                
                .empty-state {
                    text-align: center;
                    padding: 3rem;
                    color: #64748b;
                    background: white;
                    border-radius: 8px;
                    border: 1px solid #e2e8f0;
                }
                
                .empty-icon {
                    font-size: 3rem;
                    margin-bottom: 1rem;
                }
            </style>
            <div class="subscription-list">
                ${this.subscriptions.length > 0 ? this.subscriptions.map(sub => `
                    <div class="subscription-card">
                        <div class="subscription-header">
                            <div class="subscription-name">${sub.name}</div>
                            <div class="subscription-status">
                                <span class="status-dot"></span>
                                <span>Active</span>
                            </div>
                        </div>
                        <div class="subscription-body">
                            <div class="subscription-details">
                                <div class="detail-item">
                                    <div class="detail-label">Countries</div>
                                    <div class="detail-value">
                                        <div class="tag-list">
                                            ${sub.country_codes.map(code => `<span class="tag">${code}</span>`).join('')}
                                        </div>
                                    </div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Categories</div>
                                    <div class="detail-value">
                                        <div class="tag-list">
                                            ${sub.regulation_categories.map(cat => `<span class="tag">${cat.replace('_', ' ')}</span>`).join('')}
                                        </div>
                                    </div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Priority Threshold</div>
                                    <div class="detail-value">${sub.priority_threshold}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Created</div>
                                    <div class="detail-value">${new Date(sub.created_at).toLocaleDateString()}</div>
                                </div>
                            </div>
                            <div class="subscription-actions">
                                <button class="btn btn-secondary" onclick="editSubscription('${sub.id}')">Edit</button>
                                <button class="btn btn-danger" onclick="deleteSubscription('${sub.id}')">Delete</button>
                            </div>
                        </div>
                    </div>
                `).join('') : `
                    <div class="empty-state">
                        <div class="empty-icon">📧</div>
                        <div>No subscriptions configured</div>
                        <div style="margin-top: 0.5rem; font-size: 0.875rem;">Create a subscription to receive notifications about regulatory changes.</div>
                    </div>
                `}
            </div>
        `;
    }

    addSubscription(subscription) {
        this.subscriptions.push(subscription);
        this.render();
    }

    removeSubscription(id) {
        this.subscriptions = this.subscriptions.filter(sub => sub.id !== id);
        this.render();
    }
}

// Register all components
customElements.define('country-coverage-component', CountryCoverageComponent);
customElements.define('regulation-card-component', RegulationCardComponent);
customElements.define('change-item-component', ChangeItemComponent);
customElements.define('subscription-manager-component', SubscriptionManagerComponent);