// Validation rules using functional programming and mapped types

import type {
  ValidationRule,
  ValidationRules,
  Option,
  CountryCode,
  LanguageCode,
  HSCode,
  Priority,
  RegulationCategory,
  ChangeType,
} from "../types/core.ts";
import { Some, None, isSome } from "../types/core.ts";
import type { RegulationInput } from "../types/regulation.ts";
import type { Change, ChangeSubscription } from "../types/change.ts";

// Basic validation rules
export const required: ValidationRule<unknown> = (value) =>
  value == null || value === "" ? Some("This field is required") : None();

export const minLength = (min: number): ValidationRule<string> => (value) =>
  value.length < min ? Some(`Minimum length is ${min} characters`) : None();

export const maxLength = (max: number): ValidationRule<string> => (value) =>
  value.length > max ? Some(`Maximum length is ${max} characters`) : None();

export const pattern = (regex: RegExp, message: string): ValidationRule<string> => (value) =>
  !regex.test(value) ? Some(message) : None();

export const range = (min: number, max: number): ValidationRule<number> => (value) =>
  value < min || value > max ? Some(`Value must be between ${min} and ${max}`) : None();

export const email: ValidationRule<string> = pattern(
  /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  "Please enter a valid email address"
);

export const url: ValidationRule<string> = pattern(
  /^https?:\/\/.+\..+/,
  "Please enter a valid URL"
);

// Domain-specific validation rules
export const countryCode: ValidationRule<CountryCode> = pattern(
  /^[A-Z]{2}$/,
  "Country code must be a valid ISO 3166-1 alpha-2 code"
);

export const languageCode: ValidationRule<LanguageCode> = pattern(
  /^[a-z]{2}$/,
  "Language code must be a valid ISO 639-1 code"
);

export const hsCode: ValidationRule<HSCode> = pattern(
  /^\d{4,10}$/,
  "HS code must be 4-10 digits"
);

export const priority: ValidationRule<Priority> = (value) =>
  ["critical", "high", "medium", "low"].includes(value) 
    ? None() 
    : Some("Priority must be critical, high, medium, or low");

export const regulationCategory: ValidationRule<RegulationCategory> = (value) => {
  const validCategories: RegulationCategory[] = [
    "tariff",
    "non_tariff_barrier", 
    "technical_regulation",
    "sanitary_phytosanitary",
    "trade_remedies",
    "services_regulation",
    "intellectual_property",
    "government_procurement",
    "customs_procedure",
    "trade_facilitation"
  ];
  
  return validCategories.includes(value) 
    ? None() 
    : Some("Invalid regulation category");
};

export const changeType: ValidationRule<ChangeType> = (value) => {
  const validTypes: ChangeType[] = [
    "new_regulation",
    "amendment",
    "repeal",
    "effective_date_change",
    "scope_modification",
    "rate_adjustment"
  ];
  
  return validTypes.includes(value) 
    ? None() 
    : Some("Invalid change type");
};

export const futureDate: ValidationRule<Date> = (value) =>
  value <= new Date() ? Some("Date must be in the future") : None();

export const pastDate: ValidationRule<Date> = (value) =>
  value >= new Date() ? Some("Date must be in the past") : None();

export const impactScore: ValidationRule<number> = range(0, 10);

// Validation rule sets for complex objects
export const regulationValidationRules: ValidationRules<RegulationInput> = {
  country_code: countryCode,
  source_agency: required,
  title: required,
  description: required,
  category: regulationCategory,
  subcategory: required,
  hs_codes: required,
  timeline: required,
  impact_assessment: required,
  related_regulations: required,
  original_language: languageCode,
  document_metadata: required,
};

export const changeValidationRules: ValidationRules<Omit<Change, "id" | "detected_at" | "notification_sent">> = {
  regulation_id: required,
  change_type: changeType,
  priority: priority,
  impact_score: required,
  summary: required,
  detailed_changes: required,
  affected_industries: required,
  implementation_date: required,
  stakeholders: required,
  verified_at: required,
};

export const subscriptionValidationRules: ValidationRules<Omit<ChangeSubscription, "id" | "created_at" | "updated_at">> = {
  user_id: required,
  name: minLength(1),
  country_codes: required,
  regulation_categories: required,
  hs_codes: required,
  priority_threshold: priority,
  notification_config: required,
  active: required,
};

// Validation function that applies rules to an object
export const validateObject = <T>(
  obj: T,
  rules: ValidationRules<T>
): Option<Record<keyof T, string>> => {
  const errors: Partial<Record<keyof T, string>> = {};
  let hasErrors = false;

  for (const [key, rule] of Object.entries(rules) as [keyof T, ValidationRule<T[keyof T]>][]) {
    const value = obj[key];
    const validationResult = rule(value);
    
    if (isSome(validationResult)) {
      errors[key] = validationResult.value;
      hasErrors = true;
    }
  }

  return hasErrors ? Some(errors as Record<keyof T, string>) : None();
};

// Compose multiple validation rules
export const compose = <T>(...rules: ValidationRule<T>[]): ValidationRule<T> => (value) => {
  for (const rule of rules) {
    const result = rule(value);
    if (isSome(result)) {
      return result;
    }
  }
  return None();
};

// Conditional validation (apply rule only if condition is met)
export const when = <T>(
  condition: (value: T) => boolean,
  rule: ValidationRule<T>
): ValidationRule<T> => (value) =>
  condition(value) ? rule(value) : None();

// Array validation
export const arrayOf = <T>(rule: ValidationRule<T>): ValidationRule<T[]> => (values) => {
  for (let i = 0; i < values.length; i++) {
    const result = rule(values[i]);
    if (isSome(result)) {
      return Some(`Item ${i + 1}: ${result.value}`);
    }
  }
  return None();
};

export const minItems = (min: number): ValidationRule<unknown[]> => (value) =>
  value.length < min ? Some(`Minimum ${min} items required`) : None();

export const maxItems = (max: number): ValidationRule<unknown[]> => (value) =>
  value.length > max ? Some(`Maximum ${max} items allowed`) : None();

// Utility functions for working with validation results
export const hasValidationErrors = <T>(result: Option<Record<keyof T, string>>): boolean =>
  isSome(result);

export const getValidationErrors = <T>(result: Option<Record<keyof T, string>>): Record<keyof T, string> | null =>
  isSome(result) ? result.value : null;

export const getFieldError = <T>(
  result: Option<Record<keyof T, string>>,
  field: keyof T
): string | null =>
  isSome(result) ? result.value[field] || null : null;