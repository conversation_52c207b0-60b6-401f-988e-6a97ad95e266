// Regulation-specific types using Algebraic Data Types

import type {
  CountryCode,
  LanguageCode,
  HSCode,
  RegulationCategory,
  ImpactScore,
  Timeline,
  MultiLangText,
  DocumentMetadata,
} from "./core.ts";

// Core regulation entity
export type Regulation = {
  readonly id: string;
  readonly country_code: CountryCode;
  readonly source_agency: string;
  readonly title: MultiLangText;
  readonly description: MultiLangText;
  readonly category: RegulationCategory;
  readonly subcategory: string;
  readonly hs_codes: readonly HSCode[];
  readonly timeline: Timeline;
  readonly impact_assessment: ImpactScore;
  readonly related_regulations: readonly string[];
  readonly original_language: LanguageCode;
  readonly document_metadata: DocumentMetadata;
  readonly version: number;
  readonly created_at: Date;
  readonly updated_at: Date;
};

// Regulation creation input (omits computed fields)
export type RegulationInput = Omit<
  Regulation,
  "id" | "version" | "created_at" | "updated_at"
>;

// Regulation update input (partial fields except id)
export type RegulationUpdate = {
  readonly id: string;
} & Partial<Omit<Regulation, "id" | "created_at">>;

// Regulation search filters
export type RegulationFilters = {
  readonly country_codes?: readonly CountryCode[];
  readonly categories?: readonly RegulationCategory[];
  readonly hs_codes?: readonly HSCode[];
  readonly effective_after?: Date;
  readonly effective_before?: Date;
  readonly impact_score_min?: number;
  readonly search_text?: string;
  readonly languages?: readonly LanguageCode[];
};

// Regulation search result with metadata
export type RegulationSearchResult = {
  readonly regulation: Regulation;
  readonly relevance_score: number;
  readonly matched_fields: readonly string[];
  readonly highlighted_text?: string;
};

// Paginated regulation results
export type RegulationPage = {
  readonly items: readonly RegulationSearchResult[];
  readonly total_count: number;
  readonly page: number;
  readonly page_size: number;
  readonly has_next: boolean;
  readonly has_previous: boolean;
};

// Regulation statistics
export type RegulationStats = {
  readonly total_regulations: number;
  readonly by_country: ReadonlyMap<CountryCode, number>;
  readonly by_category: ReadonlyMap<RegulationCategory, number>;
  readonly recent_changes: number;
  readonly languages_supported: readonly LanguageCode[];
  readonly last_updated: Date;
};

// Constructor functions following functional programming patterns
export const createRegulation = (input: RegulationInput): Regulation => ({
  ...input,
  id: crypto.randomUUID(),
  version: 1,
  created_at: new Date(),
  updated_at: new Date(),
});

export const updateRegulation = (
  existing: Regulation,
  update: Omit<RegulationUpdate, "id">,
): Regulation => ({
  ...existing,
  ...update,
  version: existing.version + 1,
  updated_at: new Date(),
});

// Helper functions for working with regulations
export const getRegulationTitle = (
  regulation: Regulation,
  preferredLanguage: LanguageCode = "en",
): string => {
  const title = regulation.title[preferredLanguage] ||
    regulation.title[regulation.original_language] ||
    Object.values(regulation.title)[0];
  
  return title || "Untitled Regulation";
};

export const getRegulationDescription = (
  regulation: Regulation,
  preferredLanguage: LanguageCode = "en",
): string => {
  const description = regulation.description[preferredLanguage] ||
    regulation.description[regulation.original_language] ||
    Object.values(regulation.description)[0];
  
  return description || "No description available";
};

export const isRegulationActive = (regulation: Regulation): boolean => {
  const now = new Date();
  const effectiveDate = regulation.timeline.effective_date;
  const implementationDeadline = regulation.timeline.implementation_deadline;
  
  return now >= effectiveDate && 
    (implementationDeadline ? now <= implementationDeadline : true);
};

export const calculateOverallImpactScore = (impact: ImpactScore): number => {
  const weights = {
    economic: 0.3,
    operational: 0.25,
    compliance: 0.25,
    urgency: 0.2,
  };
  
  return (
    impact.economic * weights.economic +
    impact.operational * weights.operational +
    impact.compliance * weights.compliance +
    impact.urgency * weights.urgency
  );
};

export const getRegulationsByCountry = (
  regulations: readonly Regulation[],
  countryCode: CountryCode,
): readonly Regulation[] =>
  regulations.filter((reg) => reg.country_code === countryCode);

export const getRegulationsByCategory = (
  regulations: readonly Regulation[],
  category: RegulationCategory,
): readonly Regulation[] =>
  regulations.filter((reg) => reg.category === category);

export const sortRegulationsByImpact = (
  regulations: readonly Regulation[],
): readonly Regulation[] =>
  [...regulations].sort((a, b) =>
    calculateOverallImpactScore(b.impact_assessment) -
    calculateOverallImpactScore(a.impact_assessment)
  );

export const sortRegulationsByDate = (
  regulations: readonly Regulation[],
  ascending = false,
): readonly Regulation[] =>
  [...regulations].sort((a, b) => {
    const dateA = a.timeline.effective_date.getTime();
    const dateB = b.timeline.effective_date.getTime();
    return ascending ? dateA - dateB : dateB - dateA;
  });