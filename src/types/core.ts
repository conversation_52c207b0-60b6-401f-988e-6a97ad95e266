// Core Algebraic Data Types for WTO Trade Compliance System

// Result type for functional error handling
export type Result<T, E = Error> =
  | { readonly success: true; readonly data: T }
  | { readonly success: false; readonly error: E };

// Option type for nullable values
export type Option<T> =
  | { readonly some: true; readonly value: T }
  | { readonly none: true };

// ISO 3166-1 alpha-2 country codes
export type CountryCode = string;

// ISO 639-1 language codes
export type LanguageCode = string;

// Harmonized System codes for trade classification
export type HSCode = string;

// Priority levels for changes and notifications
export type Priority = "critical" | "high" | "medium" | "low";

// Regulation categories based on WTO classifications
export type RegulationCategory =
  | "tariff"
  | "non_tariff_barrier"
  | "technical_regulation"
  | "sanitary_phytosanitary"
  | "trade_remedies"
  | "services_regulation"
  | "intellectual_property"
  | "government_procurement"
  | "customs_procedure"
  | "trade_facilitation";

// Change types for regulation modifications
export type ChangeType =
  | "new_regulation"
  | "amendment"
  | "repeal"
  | "effective_date_change"
  | "scope_modification"
  | "rate_adjustment";

// Impact assessment scoring
export type ImpactScore = {
  readonly economic: number; // 0-10 scale
  readonly operational: number; // 0-10 scale
  readonly compliance: number; // 0-10 scale
  readonly urgency: number; // 0-10 scale
};

// Time-based information
export type Timeline = {
  readonly effective_date: Date;
  readonly implementation_deadline?: Date;
  readonly consultation_period?: {
    readonly start: Date;
    readonly end: Date;
  };
};

// Multi-language content support
export type MultiLangText = {
  readonly [K in LanguageCode]?: string;
};

// Document metadata
export type DocumentMetadata = {
  readonly source_url: string;
  readonly document_hash: string;
  readonly content_type: string;
  readonly file_size: number;
  readonly extracted_at: Date;
  readonly last_modified?: Date;
};

// Validation rule type for form validation
export type ValidationRule<T> = (value: T) => Option<string>;

// Generic validation rules mapped to object properties
export type ValidationRules<T> = {
  readonly [K in keyof T]: ValidationRule<T[K]>;
};

// Helper functions for Result type
export const Ok = <T>(data: T): Result<T, never> => ({
  success: true,
  data,
});

export const Err = <E>(error: E): Result<never, E> => ({
  success: false,
  error,
});

// Helper functions for Option type
export const Some = <T>(value: T): Option<T> => ({
  some: true,
  value,
});

export const None = (): Option<never> => ({
  none: true,
});

// Utility functions for working with Result
export const isOk = <T, E>(result: Result<T, E>): result is { success: true; data: T } =>
  result.success;

export const isErr = <T, E>(result: Result<T, E>): result is { success: false; error: E } =>
  !result.success;

// Utility functions for working with Option
export const isSome = <T>(option: Option<T>): option is { some: true; value: T } =>
  "some" in option && option.some;

export const isNone = <T>(option: Option<T>): option is { none: true } =>
  "none" in option && option.none;

// Map function for Result
export const mapResult = <T, U, E>(
  result: Result<T, E>,
  fn: (data: T) => U,
): Result<U, E> => {
  if (isOk(result)) {
    return Ok(fn(result.data));
  } else {
    return result as Result<U, E>;
  }
};

// FlatMap function for Result (monadic bind)
export const flatMapResult = <T, U, E>(
  result: Result<T, E>,
  fn: (data: T) => Result<U, E>,
): Result<U, E> => {
  if (isOk(result)) {
    return fn(result.data);
  } else {
    return result as Result<U, E>;
  }
};

// Map function for Option
export const mapOption = <T, U>(
  option: Option<T>,
  fn: (value: T) => U,
): Option<U> =>
  isSome(option) ? Some(fn(option.value)) : None();

// FlatMap function for Option
export const flatMapOption = <T, U>(
  option: Option<T>,
  fn: (value: T) => Option<U>,
): Option<U> =>
  isSome(option) ? fn(option.value) : None();

// Processing state for async operations
export type ProcessingState<T> = 
  | { readonly status: "idle" }
  | { readonly status: "loading" }
  | { readonly status: "success"; readonly data: T }
  | { readonly status: "error"; readonly error: Error };

// Pipeline stage for data processing
export type PipelineStage<I, O> = (input: I) => Promise<Result<O>>;

// Compose pipeline stages
export const composePipeline = <A, B, C>(
  stage1: PipelineStage<A, B>,
  stage2: PipelineStage<B, C>
): PipelineStage<A, C> => async (input: A) => {
  const result1 = await stage1(input);
  if (!result1.success) {
    return result1 as Result<C>;
  }
  return await stage2(result1.data);
};

// Helper for processing arrays with Result types
export const collectResults = <T, E>(
  results: readonly Result<T, E>[]
): Result<readonly T[], readonly E[]> => {
  const successes: T[] = [];
  const errors: E[] = [];
  
  for (const result of results) {
    if (result.success) {
      successes.push(result.data);
    } else {
      errors.push(result.error);
    }
  }
  
  return errors.length === 0 
    ? Ok(successes)
    : Err(errors);
};