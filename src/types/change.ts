// Change detection and notification types

import type {
  Priority,
  ChangeType,
  ImpactScore,
  MultiLangText,
} from "./core.ts";

// Change entity representing modifications to regulations
export type Change = {
  readonly id: string;
  readonly regulation_id: string;
  readonly change_type: ChangeType;
  readonly priority: Priority;
  readonly impact_score: ImpactScore;
  readonly summary: MultiLangText;
  readonly detailed_changes: ChangeDetail[];
  readonly affected_industries: readonly string[];
  readonly implementation_date: Date;
  readonly stakeholders: readonly string[];
  readonly detected_at: Date;
  readonly verified_at?: Date;
  readonly notification_sent: boolean;
};

// Detailed change information
export type ChangeDetail = {
  readonly field: string;
  readonly previous_value: unknown;
  readonly new_value: unknown;
  readonly change_description: string;
  readonly confidence_score: number; // 0-1 indicating detection confidence
};

// Change detection algorithm result
export type ChangeDetectionResult = {
  readonly changes_detected: readonly Change[];
  readonly similarity_score: number; // 0-1, higher means more similar
  readonly detection_confidence: number; // 0-1, confidence in detection
  readonly processing_time_ms: number;
  readonly algorithm_version: string;
};

// Change notification configuration
export type NotificationConfig = {
  readonly enabled: boolean;
  readonly priority_threshold: Priority;
  readonly channels: readonly NotificationChannel[];
  readonly delay_minutes: number;
  readonly batch_notifications: boolean;
  readonly custom_filters: readonly NotificationFilter[];
};

// Notification channels
export type NotificationChannel =
  | { readonly type: "email"; readonly addresses: readonly string[] }
  | { readonly type: "webhook"; readonly url: string; readonly headers?: Record<string, string> }
  | { readonly type: "sms"; readonly numbers: readonly string[] }
  | { readonly type: "slack"; readonly webhook_url: string; readonly channel: string };

// Notification filters
export type NotificationFilter = {
  readonly field: keyof Change;
  readonly operator: "equals" | "contains" | "greater_than" | "less_than";
  readonly value: unknown;
};

// Change subscription for users/organizations
export type ChangeSubscription = {
  readonly id: string;
  readonly user_id: string;
  readonly name: string;
  readonly country_codes: readonly string[];
  readonly regulation_categories: readonly string[];
  readonly hs_codes: readonly string[];
  readonly priority_threshold: Priority;
  readonly notification_config: NotificationConfig;
  readonly created_at: Date;
  readonly updated_at: Date;
  readonly active: boolean;
};

// Change analytics and statistics
export type ChangeAnalytics = {
  readonly total_changes: number;
  readonly changes_by_priority: ReadonlyMap<Priority, number>;
  readonly changes_by_type: ReadonlyMap<ChangeType, number>;
  readonly changes_by_country: ReadonlyMap<string, number>;
  readonly average_detection_time_minutes: number;
  readonly detection_accuracy: number;
  readonly notification_delivery_rate: number;
  readonly period_start: Date;
  readonly period_end: Date;
};

// Constructor functions
export const createChange = (
  regulation_id: string,
  change_type: ChangeType,
  summary: MultiLangText,
  detailed_changes: ChangeDetail[],
): Change => ({
  id: crypto.randomUUID(),
  regulation_id,
  change_type,
  priority: calculateChangePriority(detailed_changes),
  impact_score: calculateChangeImpact(detailed_changes),
  summary,
  detailed_changes,
  affected_industries: [],
  implementation_date: new Date(),
  stakeholders: [],
  detected_at: new Date(),
  notification_sent: false,
});

export const createChangeDetail = (
  field: string,
  previous_value: unknown,
  new_value: unknown,
  description: string,
  confidence = 1.0,
): ChangeDetail => ({
  field,
  previous_value,
  new_value,
  change_description: description,
  confidence_score: Math.max(0, Math.min(1, confidence)),
});

// Helper functions
export const calculateChangePriority = (details: readonly ChangeDetail[]): Priority => {
  const avgConfidence = details.reduce((sum, detail) => sum + detail.confidence_score, 0) / details.length;
  
  if (avgConfidence >= 0.9) return "critical";
  if (avgConfidence >= 0.7) return "high";
  if (avgConfidence >= 0.5) return "medium";
  return "low";
};

export const calculateChangeImpact = (details: readonly ChangeDetail[]): ImpactScore => {
  const baseScore = details.length > 5 ? 8 : details.length > 2 ? 6 : 4;
  const avgConfidence = details.reduce((sum, detail) => sum + detail.confidence_score, 0) / details.length;
  
  return {
    economic: Math.min(10, baseScore * avgConfidence),
    operational: Math.min(10, (baseScore - 1) * avgConfidence),
    compliance: Math.min(10, (baseScore + 1) * avgConfidence),
    urgency: Math.min(10, baseScore * avgConfidence * 0.8),
  };
};

export const isHighPriorityChange = (change: Change): boolean =>
  change.priority === "critical" || change.priority === "high";

export const getChangesByPriority = (
  changes: readonly Change[],
  priority: Priority,
): readonly Change[] =>
  changes.filter((change) => change.priority === priority);

export const getRecentChanges = (
  changes: readonly Change[],
  hoursBack = 24,
): readonly Change[] => {
  const cutoffTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000);
  return changes.filter((change) => change.detected_at > cutoffTime);
};

export const sortChangesByImpact = (changes: readonly Change[]): readonly Change[] =>
  [...changes].sort((a, b) => {
    const scoreA = a.impact_score.compliance + a.impact_score.urgency;
    const scoreB = b.impact_score.compliance + b.impact_score.urgency;
    return scoreB - scoreA;
  });

export const groupChangesByRegulation = (
  changes: readonly Change[],
): ReadonlyMap<string, readonly Change[]> => {
  const grouped = new Map<string, Change[]>();
  
  for (const change of changes) {
    const existing = grouped.get(change.regulation_id) || [];
    grouped.set(change.regulation_id, [...existing, change]);
  }
  
  return new Map([...grouped.entries()].map(([key, value]) => [key, value as readonly Change[]]));
};

export const filterChangesByDateRange = (
  changes: readonly Change[],
  startDate: Date,
  endDate: Date,
): readonly Change[] =>
  changes.filter((change) => 
    change.detected_at >= startDate && change.detected_at <= endDate
  );