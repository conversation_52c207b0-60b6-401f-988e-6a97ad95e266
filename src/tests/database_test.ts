// Database service tests
import { assertEquals, assertExists } from "@std/assert";
import { createDatabaseService } from "../services/database.ts";
import type { CrawlJob } from "../services/crawling.ts";
import type { WebSource } from "../services/firecrawl.ts";

// Mock logger
const mockLogger = {
  info: () => {},
  error: () => {},
  warn: () => {},
  debug: () => {},
};

Deno.test("Database Service - Job Management", async () => {
  const dbResult = await createDatabaseService({ kvPath: ":memory:" }, mockLogger);
  
  if (!dbResult.success) {
    throw new Error("Failed to create database service");
  }
  
  const db = dbResult.data;
  
  try {
    // Test job creation
    const job: CrawlJob = {
      id: "test-job-1",
      webSourceId: "test-source",
      status: "pending",
      startedAt: new Date(),
      pagesFound: 0,
      regulationsExtracted: 0,
      changesDetected: 0,
    };
    
    const saveResult = await db.saveJob(job);
    assertEquals(saveResult.success, true);
    
    // Test job retrieval
    const getResult = await db.getJob("test-job-1");
    assertEquals(getResult.success, true);
    
    if (getResult.success) {
      assertEquals(getResult.data.id, "test-job-1");
      assertEquals(getResult.data.status, "pending");
    }
    
    // Test job status update
    const updateResult = await db.updateJobStatus("test-job-1", "running");
    assertEquals(updateResult.success, true);
    
    // Verify update
    const updatedResult = await db.getJob("test-job-1");
    assertEquals(updatedResult.success, true);
    
    if (updatedResult.success) {
      assertEquals(updatedResult.data.status, "running");
    }
    
    // Test jobs by status
    const jobsByStatusResult = await db.getJobsByStatus("running");
    assertEquals(jobsByStatusResult.success, true);
    
    if (jobsByStatusResult.success) {
      assertEquals(jobsByStatusResult.data.length, 1);
      assertEquals(jobsByStatusResult.data[0].id, "test-job-1");
    }
    
    console.log("✅ Database job management tests passed");
  } finally {
    await db.close();
  }
});

Deno.test("Database Service - Web Source Management", async () => {
  const dbResult = await createDatabaseService({ kvPath: ":memory:" }, mockLogger);
  
  if (!dbResult.success) {
    throw new Error("Failed to create database service");
  }
  
  const db = dbResult.data;
  
  try {
    // Test web source creation
    const webSource: WebSource = {
      id: "test-source-1",
      name: "Test Source",
      url: "https://example.com",
      country: "US",
      agency: "Test Agency",
      category: ["technical_regulation"],
      crawlConfig: {
        maxDepth: 2,
        limit: 10,
      },
      schedule: "0 */6 * * *",
      active: true,
    };
    
    const saveResult = await db.saveWebSource(webSource);
    assertEquals(saveResult.success, true);
    
    // Test web source retrieval
    const getResult = await db.getWebSource("test-source-1");
    assertEquals(getResult.success, true);
    
    if (getResult.success) {
      assertEquals(getResult.data.id, "test-source-1");
      assertEquals(getResult.data.name, "Test Source");
      assertEquals(getResult.data.active, true);
    }
    
    // Test active web sources
    const activeResult = await db.getActiveWebSources();
    assertEquals(activeResult.success, true);
    
    if (activeResult.success) {
      assertEquals(activeResult.data.length, 1);
      assertEquals(activeResult.data[0].id, "test-source-1");
    }
    
    // Test web source update
    const updateResult = await db.updateWebSource("test-source-1", { active: false });
    assertEquals(updateResult.success, true);
    
    // Verify update
    const updatedResult = await db.getWebSource("test-source-1");
    assertEquals(updatedResult.success, true);
    
    if (updatedResult.success) {
      assertEquals(updatedResult.data.active, false);
    }
    
    // Test active sources after update
    const activeAfterUpdateResult = await db.getActiveWebSources();
    assertEquals(activeAfterUpdateResult.success, true);
    
    if (activeAfterUpdateResult.success) {
      assertEquals(activeAfterUpdateResult.data.length, 0);
    }
    
    console.log("✅ Database web source management tests passed");
  } finally {
    await db.close();
  }
});

Deno.test("Database Service - Regulation Management", async () => {
  const dbResult = await createDatabaseService({ kvPath: ":memory:" }, mockLogger);
  
  if (!dbResult.success) {
    throw new Error("Failed to create database service");
  }
  
  const db = dbResult.data;
  
  try {
    // Test regulation creation
    const regulation = {
      country_code: "US",
      source_agency: "Test Agency",
      title: { en: "Test Regulation" },
      description: { en: "A test regulation for database testing" },
      category: "technical_regulation" as const,
      subcategory: "testing" as const,
      hs_codes: ["1234", "5678"],
      timeline: {
        effective_date: new Date("2024-01-01"),
      },
      impact_assessment: {
        economic: 5,
        operational: 5,
        compliance: 5,
        urgency: 5,
      },
      related_regulations: [],
      original_language: "en" as const,
      document_metadata: {
        source_url: "https://example.com/regulation",
        document_hash: "test-hash",
        content_type: "text/html",
        file_size: 1000,
        extracted_at: new Date(),
      },
    };
    
    const saveResult = await db.saveRegulation(regulation);
    assertEquals(saveResult.success, true);
    
    if (saveResult.success) {
      const regulationId = saveResult.data;
      assertExists(regulationId);
      
      // Test regulation retrieval
      const getResult = await db.getRegulation(regulationId);
      assertEquals(getResult.success, true);
      
      if (getResult.success) {
        assertEquals(getResult.data.country_code, "US");
        assertEquals(getResult.data.title.en, "Test Regulation");
        assertExists(getResult.data.created_at);
        assertExists(getResult.data.updated_at);
      }
      
      // Test regulation search by country
      const findResult = await db.findRegulations({ country: "US" });
      assertEquals(findResult.success, true);
      
      if (findResult.success) {
        assertEquals(findResult.data.length, 1);
        assertEquals(findResult.data[0].id, regulationId);
      }
    }
    
    console.log("✅ Database regulation management tests passed");
  } finally {
    await db.close();
  }
});

Deno.test("Database Service - Performance Test", async () => {
  const dbResult = await createDatabaseService({ kvPath: ":memory:" }, mockLogger);
  
  if (!dbResult.success) {
    throw new Error("Failed to create database service");
  }
  
  const db = dbResult.data;
  
  try {
    const startTime = Date.now();
    
    // Create multiple jobs
    const jobs: CrawlJob[] = [];
    for (let i = 0; i < 10; i++) {
      const job: CrawlJob = {
        id: `perf-test-job-${i}`,
        webSourceId: `source-${i}`,
        status: i % 2 === 0 ? "pending" : "running",
        startedAt: new Date(),
        pagesFound: i * 5,
        regulationsExtracted: i * 2,
        changesDetected: i,
      };
      
      const saveResult = await db.saveJob(job);
      assertEquals(saveResult.success, true);
      jobs.push(job);
    }
    
    // Retrieve all jobs
    const allJobsResult = await db.getAllJobs(20);
    assertEquals(allJobsResult.success, true);
    
    if (allJobsResult.success) {
      assertEquals(allJobsResult.data.length, 10);
    }
    
    // Get jobs by status
    const pendingJobsResult = await db.getJobsByStatus("pending");
    assertEquals(pendingJobsResult.success, true);
    
    if (pendingJobsResult.success) {
      assertEquals(pendingJobsResult.data.length, 5);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ Database performance test passed (${duration}ms for 10 jobs)`);
  } finally {
    await db.close();
  }
});
