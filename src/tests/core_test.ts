// Behavior-driven tests for core functional types

import { assertEquals, assertExists } from "@std/assert";
import { 
  Ok, 
  Err, 
  Some, 
  None, 
  isOk, 
  isErr, 
  isSome, 
  isNone,
  mapResult,
  flatMapResult,
  mapOption,
  flatMapOption,
  collectResults,
  composePipeline 
} from "../types/core.ts";

// Result type behavior tests
Deno.test("Result type should create successful results with data", () => {
  // Given a successful operation value
  const value = { id: "test", name: "Test Item" };
  
  // When creating an Ok result
  const result = Ok(value);
  
  // Then it should be recognized as successful
  assertEquals(isOk(result), true);
  assertEquals(isErr(result), false);
  
  // And contain the expected data
  if (isOk(result)) {
    assertEquals(result.data, value);
  }
});

Deno.test("Result type should create error results with error information", () => {
  // Given an error condition
  const error = new Error("Operation failed");
  
  // When creating an Err result
  const result = Err(error);
  
  // Then it should be recognized as an error
  assertEquals(isOk(result), false);
  assertEquals(isErr(result), true);
  
  // And contain the error information
  if (isErr(result)) {
    assertEquals(result.error.message, "Operation failed");
  }
});

// Option type behavior tests
Deno.test("Option type should handle presence of values", () => {
  // Given a value that exists
  const value = "hello world";
  
  // When creating a Some option
  const option = Some(value);
  
  // Then it should be recognized as having a value
  assertEquals(isSome(option), true);
  assertEquals(isNone(option), false);
  
  // And contain the expected value
  if (isSome(option)) {
    assertEquals(option.value, value);
  }
});

Deno.test("Option type should handle absence of values", () => {
  // Given no value
  // When creating a None option
  const option = None();
  
  // Then it should be recognized as having no value
  assertEquals(isSome(option), false);
  assertEquals(isNone(option), true);
});

// Functional transformation behavior tests
Deno.test("mapResult should transform successful results", () => {
  // Given a successful result with a number
  const result = Ok(5);
  
  // When mapping the result with a transformation function
  const mapped = mapResult(result, (x) => x * 2);
  
  // Then the result should still be successful
  assertEquals(isOk(mapped), true);
  
  // And contain the transformed value
  if (isOk(mapped)) {
    assertEquals(mapped.data, 10);
  }
});

Deno.test("mapResult should preserve errors without transformation", () => {
  // Given a failed result
  const error = new Error("calculation failed");
  const result = Err(error);
  
  // When attempting to map the result
  const mapped = mapResult(result, (x: number) => x * 2);
  
  // Then the result should still be an error
  assertEquals(isErr(mapped), true);
  
  // And preserve the original error
  if (isErr(mapped)) {
    assertEquals(mapped.error.message, "calculation failed");
  }
});

Deno.test("flatMapResult should chain transformations that may fail", () => {
  // Given a successful result
  const result = Ok(5);
  
  // When flat-mapping with a function that returns a Result
  const flatMapped = flatMapResult(result, (x) => Ok(x.toString()));
  
  // Then the final result should be successful
  assertEquals(isOk(flatMapped), true);
  
  // And contain the chained transformation result
  if (isOk(flatMapped)) {
    assertEquals(flatMapped.data, "5");
  }
});

Deno.test("flatMapResult should propagate errors through the chain", () => {
  // Given a failed result
  const error = new Error("initial failure");
  const result = Err(error);
  
  // When flat-mapping (which won't be executed due to the error)
  const flatMapped = flatMapResult(result, (x: number) => Ok(x.toString()));
  
  // Then the error should propagate through
  assertEquals(isErr(flatMapped), true);
  
  // And preserve the original error
  if (isErr(flatMapped)) {
    assertEquals(flatMapped.error.message, "initial failure");
  }
});

// Option transformation behavior tests
Deno.test("mapOption should transform present values", () => {
  // Given an option with a value
  const option = Some(10);
  
  // When mapping with a transformation function
  const mapped = mapOption(option, (x) => x * 3);
  
  // Then the option should still contain a value
  assertEquals(isSome(mapped), true);
  
  // And the value should be transformed
  if (isSome(mapped)) {
    assertEquals(mapped.value, 30);
  }
});

Deno.test("mapOption should preserve None through transformations", () => {
  // Given an option with no value
  const option = None();
  
  // When attempting to map the option
  const mapped = mapOption(option, (x: number) => x * 3);
  
  // Then it should remain None
  assertEquals(isNone(mapped), true);
});

Deno.test("flatMapOption should handle conditional transformations", () => {
  // Given an option with a value
  const option = Some(7);
  
  // When flat-mapping with conditional logic
  const flatMapped = flatMapOption(option, (x) => Some(x > 5 ? "big" : "small"));
  
  // Then the result should contain the conditional result
  assertEquals(isSome(flatMapped), true);
  if (isSome(flatMapped)) {
    assertEquals(flatMapped.value, "big");
  }
});

Deno.test("flatMapOption should propagate None values", () => {
  // Given an option with no value
  const option = None();
  
  // When flat-mapping (which won't be executed)
  const flatMapped = flatMapOption(option, (x: number) => Some(x.toString()));
  
  // Then the result should remain None
  assertEquals(isNone(flatMapped), true);
});

// Collection and pipeline behavior tests
Deno.test("collectResults should separate successes from failures", () => {
  // Given a mix of successful and failed results
  const results = [
    Ok("success1"),
    Err(new Error("error1")),
    Ok("success2"),
    Err(new Error("error2")),
  ];
  
  // When collecting the results
  const collected = collectResults(results);
  
  // Then failures should be collected in the error case
  assertEquals(isErr(collected), true);
  if (isErr(collected)) {
    assertEquals(collected.error.length, 2);
    assertEquals(collected.error[0].message, "error1");
    assertEquals(collected.error[1].message, "error2");
  }
});

Deno.test("collectResults should return all successes when no failures", () => {
  // Given only successful results
  const results = [
    Ok("success1"),
    Ok("success2"),
    Ok("success3"),
  ];
  
  // When collecting the results
  const collected = collectResults(results);
  
  // Then all successes should be returned
  assertEquals(isOk(collected), true);
  if (isOk(collected)) {
    assertEquals(collected.data.length, 3);
    assertEquals(collected.data, ["success1", "success2", "success3"]);
  }
});