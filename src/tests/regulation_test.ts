// Regulation type tests

import { assertEquals } from "@std/assert";
import { 
  createRegulation,
  updateRegulation,
  getRegulationTitle,
  getRegulationDescription,
  isRegulationActive,
  calculateOverallImpactScore,
  sortRegulationsByImpact
} from "../types/regulation.ts";
import type { RegulationInput } from "../types/regulation.ts";

const sampleRegulationInput: RegulationInput = {
  country_code: "US",
  source_agency: "US Trade.gov",
  title: { en: "New Tariff Regulation" },
  description: { en: "This regulation introduces new tariff rates" },
  category: "tariff",
  subcategory: "ad valorem",
  hs_codes: ["8471", "8473"],
  timeline: {
    effective_date: new Date("2024-01-01"),
    implementation_deadline: new Date("2024-06-01"),
  },
  impact_assessment: {
    economic: 8,
    operational: 6,
    compliance: 7,
    urgency: 9,
  },
  related_regulations: [],
  original_language: "en",
  document_metadata: {
    source_url: "https://example.com/regulation",
    document_hash: "abc123",
    content_type: "text/html",
    file_size: 1024,
    extracted_at: new Date(),
  },
};

Deno.test("createRegulation - generates ID and timestamps", () => {
  const regulation = createRegulation(sampleRegulationInput);
  
  assertEquals(typeof regulation.id, "string");
  assertEquals(regulation.id.length > 0, true);
  assertEquals(regulation.version, 1);
  assertEquals(regulation.created_at instanceof Date, true);
  assertEquals(regulation.updated_at instanceof Date, true);
  assertEquals(regulation.country_code, "US");
  assertEquals(regulation.category, "tariff");
});

Deno.test("updateRegulation - increments version and updates timestamp", async () => {
  const original = createRegulation(sampleRegulationInput);
  
  // Wait a tiny bit to ensure different timestamps
  await new Promise(resolve => setTimeout(resolve, 1));
  
  const updated = updateRegulation(original, {
    title: { en: "Updated Tariff Regulation" },
    subcategory: "specific",
  });
  
  assertEquals(updated.id, original.id);
  assertEquals(updated.version, original.version + 1);
  assertEquals(updated.title.en, "Updated Tariff Regulation");
  assertEquals(updated.subcategory, "specific");
  assertEquals(updated.created_at, original.created_at);
  assertEquals(updated.updated_at >= original.updated_at, true);
});

Deno.test("getRegulationTitle - returns preferred language", () => {
  const regulation = createRegulation({
    ...sampleRegulationInput,
    title: { en: "English Title", fr: "Titre Français", es: "Título Español" },
  });
  
  assertEquals(getRegulationTitle(regulation, "en"), "English Title");
  assertEquals(getRegulationTitle(regulation, "fr"), "Titre Français");
  assertEquals(getRegulationTitle(regulation, "de"), "English Title"); // Falls back to English
});

Deno.test("getRegulationTitle - falls back to original language", () => {
  const regulation = createRegulation({
    ...sampleRegulationInput,
    title: { es: "Título Original" },
    original_language: "es",
  });
  
  assertEquals(getRegulationTitle(regulation, "en"), "Título Original");
});

Deno.test("getRegulationDescription - returns preferred language", () => {
  const regulation = createRegulation({
    ...sampleRegulationInput,
    description: { en: "English Description", fr: "Description Française" },
  });
  
  assertEquals(getRegulationDescription(regulation, "en"), "English Description");
  assertEquals(getRegulationDescription(regulation, "fr"), "Description Française");
});

Deno.test("isRegulationActive - checks date range", () => {
  const now = new Date();
  const pastDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
  const futureDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
  
  // Active regulation (effective in past, no deadline)
  const activeReg = createRegulation({
    ...sampleRegulationInput,
    timeline: { effective_date: pastDate },
  });
  assertEquals(isRegulationActive(activeReg), true);
  
  // Not yet active (effective in future)
  const futureReg = createRegulation({
    ...sampleRegulationInput,
    timeline: { effective_date: futureDate },
  });
  assertEquals(isRegulationActive(futureReg), false);
  
  // Expired regulation (deadline passed)
  const expiredReg = createRegulation({
    ...sampleRegulationInput,
    timeline: { 
      effective_date: pastDate,
      implementation_deadline: pastDate,
    },
  });
  assertEquals(isRegulationActive(expiredReg), false);
});

Deno.test("calculateOverallImpactScore - weighted calculation", () => {
  const impact = {
    economic: 10,
    operational: 8,
    compliance: 6,
    urgency: 4,
  };
  
  const score = calculateOverallImpactScore(impact);
  
  // Expected: 10*0.3 + 8*0.25 + 6*0.25 + 4*0.2 = 3 + 2 + 1.5 + 0.8 = 7.3
  assertEquals(Math.round(score * 10) / 10, 7.3);
});

Deno.test("sortRegulationsByImpact - descending order", () => {
  const reg1 = createRegulation({
    ...sampleRegulationInput,
    impact_assessment: { economic: 5, operational: 5, compliance: 5, urgency: 5 },
  });
  
  const reg2 = createRegulation({
    ...sampleRegulationInput,
    impact_assessment: { economic: 10, operational: 10, compliance: 10, urgency: 10 },
  });
  
  const reg3 = createRegulation({
    ...sampleRegulationInput,
    impact_assessment: { economic: 7, operational: 7, compliance: 7, urgency: 7 },
  });
  
  const sorted = sortRegulationsByImpact([reg1, reg2, reg3]);
  
  assertEquals(sorted[0].id, reg2.id); // Highest impact first
  assertEquals(sorted[1].id, reg3.id);
  assertEquals(sorted[2].id, reg1.id); // Lowest impact last
});