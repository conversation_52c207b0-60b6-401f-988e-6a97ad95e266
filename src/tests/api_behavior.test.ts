// Behavior-driven tests for API endpoints
import { assertEquals, assertExists } from "@std/assert";
import { createServices } from "../server/dependencies.ts";
import { createApiRoutes } from "../server/routes.ts";
import { createRouter } from "../lib/router.ts";

// Test server setup
const createTestServer = () => {
  const services = createServices();
  const apiRoutes = createApiRoutes(services);
  
  return createRouter(apiRoutes, {
    cors: {
      origin: "*",
      methods: ["GET", "POST", "PUT", "DELETE"],
      headers: ["Content-Type", "Authorization"],
    },
  });
};

// Behavior: Getting regulations
Deno.test("API should return regulations list when requested", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When requesting the regulations endpoint
  const request = new Request("http://localhost/api/regulations");
  const response = await router(request);
  
  // Then the response should be successful
  assertEquals(response.status, 200);
  
  // And contain JSON data
  const contentType = response.headers.get("content-type");
  assertEquals(contentType?.includes("application/json"), true);
  
  // And have the expected structure
  const data = await response.json();
  assertExists(data.success);
  assertEquals(data.success, true);
  assertExists(data.data);
  assertExists(data.data.regulations);
  assertExists(data.timestamp);
});

Deno.test("API should filter regulations by country when country parameter provided", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When requesting regulations for a specific country
  const request = new Request("http://localhost/api/regulations?country=US");
  const response = await router(request);
  
  // Then the response should be successful
  assertEquals(response.status, 200);
  
  // And return filtered results
  const data = await response.json();
  assertEquals(data.success, true);
  
  // And all regulations should be from the requested country
  if (data.data.regulations.length > 0) {
    data.data.regulations.forEach((reg: any) => {
      assertEquals(reg.country_code, "US");
    });
  }
});

Deno.test("API should return HTML when HTMX request is made", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When making an HTMX request for regulations
  const request = new Request("http://localhost/api/regulations", {
    headers: { "HX-Request": "true" },
  });
  const response = await router(request);
  
  // Then the response should be successful
  assertEquals(response.status, 200);
  
  // And return HTML content
  const contentType = response.headers.get("content-type");
  assertEquals(contentType?.includes("text/html"), true);
  
  // And contain HTML content
  const html = await response.text();
  assertExists(html);
});

// Behavior: Getting specific regulations
Deno.test("API should return specific regulation when valid ID provided", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When requesting a specific regulation
  const request = new Request("http://localhost/api/regulations/reg-1");
  const response = await router(request);
  
  // Then the response should be successful
  assertEquals(response.status, 200);
  
  // And contain the regulation data
  const data = await response.json();
  assertEquals(data.success, true);
  assertExists(data.data.regulation);
  assertEquals(data.data.regulation.id, "reg-1");
  assertExists(data.data.changes);
});

Deno.test("API should return 404 when regulation ID not found", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When requesting a non-existent regulation
  const request = new Request("http://localhost/api/regulations/non-existent");
  const response = await router(request);
  
  // Then the response should indicate not found
  assertEquals(response.status, 404);
  
  // And contain error information
  const data = await response.json();
  assertEquals(data.success, false);
  assertExists(data.error);
});

// Behavior: Getting changes
Deno.test("API should return changes list when requested", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When requesting the changes endpoint
  const request = new Request("http://localhost/api/changes");
  const response = await router(request);
  
  // Then the response should be successful
  assertEquals(response.status, 200);
  
  // And contain changes data
  const data = await response.json();
  assertEquals(data.success, true);
  assertExists(data.data.changes);
  assertExists(data.data.total);
});

Deno.test("API should filter changes by priority when priority parameter provided", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When requesting changes with a specific priority
  const request = new Request("http://localhost/api/changes?priority=critical");
  const response = await router(request);
  
  // Then the response should be successful
  assertEquals(response.status, 200);
  
  // And return filtered results
  const data = await response.json();
  assertEquals(data.success, true);
  
  // And all changes should have the requested priority
  if (data.data.changes.length > 0) {
    data.data.changes.forEach((change: any) => {
      assertEquals(change.priority, "critical");
    });
  }
});

// Behavior: Getting dashboard stats
Deno.test("API should return dashboard statistics when requested", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When requesting dashboard stats
  const request = new Request("http://localhost/api/dashboard/stats");
  const response = await router(request);
  
  // Then the response should be successful
  assertEquals(response.status, 200);
  
  // And contain statistical data
  const data = await response.json();
  assertEquals(data.success, true);
  assertExists(data.data.overview);
  assertExists(data.data.distributions);
  assertExists(data.data.recentActivity);
  
  // And overview should have expected metrics
  const overview = data.data.overview;
  assertExists(overview.totalRegulations);
  assertExists(overview.totalChanges);
  assertExists(overview.recentChanges);
  assertExists(overview.countriesMonitored);
});

// Behavior: Creating subscriptions
Deno.test("API should create subscription when valid data provided", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // And valid subscription data
  const subscriptionData = {
    user_id: "test-user",
    name: "Test Subscription",
    country_codes: ["US", "EU"],
    regulation_categories: ["tariff", "customs_procedure"],
    priority_threshold: "medium",
    channels: [],
  };
  
  // When creating a subscription
  const request = new Request("http://localhost/api/subscriptions", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(subscriptionData),
  });
  const response = await router(request);
  
  // Then the response should indicate creation success
  assertEquals(response.status, 201);
  
  // And contain the created subscription
  const data = await response.json();
  assertEquals(data.success, true);
  assertExists(data.data.id);
  assertEquals(data.data.user_id, "test-user");
  assertEquals(data.data.name, "Test Subscription");
  assertExists(data.data.created_at);
  assertExists(data.data.updated_at);
});

Deno.test("API should reject subscription with invalid JSON", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When sending invalid JSON
  const request = new Request("http://localhost/api/subscriptions", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: "invalid json",
  });
  const response = await router(request);
  
  // Then the response should indicate bad request
  assertEquals(response.status, 400);
  
  // And contain error information
  const data = await response.json();
  assertEquals(data.success, false);
  assertExists(data.error);
});

// Behavior: CORS handling
Deno.test("API should handle CORS preflight requests", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When making a CORS preflight request
  const request = new Request("http://localhost/api/regulations", {
    method: "OPTIONS",
  });
  const response = await router(request);
  
  // Then the response should allow CORS
  assertEquals(response.status, 200);
  
  // And include CORS headers
  const allowOrigin = response.headers.get("Access-Control-Allow-Origin");
  const allowMethods = response.headers.get("Access-Control-Allow-Methods");
  const allowHeaders = response.headers.get("Access-Control-Allow-Headers");
  
  assertEquals(allowOrigin, "*");
  assertExists(allowMethods);
  assertExists(allowHeaders);
});

// Behavior: Error handling
Deno.test("API should return 404 for unknown endpoints", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When requesting an unknown endpoint
  const request = new Request("http://localhost/api/unknown");
  const response = await router(request);
  
  // Then the response should indicate not found
  assertEquals(response.status, 404);
});

Deno.test("API should include CORS headers in error responses", async () => {
  // Given a running API server
  const router = createTestServer();
  
  // When requesting an unknown endpoint
  const request = new Request("http://localhost/api/unknown");
  const response = await router(request);
  
  // Then CORS headers should be included even in error responses
  const allowOrigin = response.headers.get("Access-Control-Allow-Origin");
  assertEquals(allowOrigin, "*");
});