// Validation tests

import { assertEquals } from "@std/assert";
import { 
  required, 
  email, 
  minLength, 
  maxLength, 
  countryCode, 
  languageCode,
  priority,
  validateObject,
  hasValidationErrors,
  getValidationErrors,
  compose
} from "../validation/rules.ts";
import { isSome, isNone } from "../types/core.ts";

Deno.test("required validation - passes for valid values", () => {
  assertEquals(isNone(required("test")), true);
  assertEquals(isNone(required(123)), true);
  assertEquals(isNone(required({})), true);
});

Deno.test("required validation - fails for null/undefined/empty", () => {
  assertEquals(isSome(required(null)), true);
  assertEquals(isSome(required(undefined)), true);
  assertEquals(isSome(required("")), true);
});

Deno.test("email validation - valid emails", () => {
  assertEquals(isNone(email("<EMAIL>")), true);
  assertEquals(isNone(email("<EMAIL>")), true);
});

Deno.test("email validation - invalid emails", () => {
  assertEquals(isSome(email("invalid")), true);
  assertEquals(isSome(email("@domain.com")), true);
  assertEquals(isSome(email("user@")), true);
});

Deno.test("minLength validation", () => {
  const min3 = minLength(3);
  assertEquals(isNone(min3("hello")), true);
  assertEquals(isNone(min3("abc")), true);
  assertEquals(isSome(min3("ab")), true);
});

Deno.test("maxLength validation", () => {
  const max5 = maxLength(5);
  assertEquals(isNone(max5("hello")), true);
  assertEquals(isNone(max5("hi")), true);
  assertEquals(isSome(max5("toolong")), true);
});

Deno.test("countryCode validation", () => {
  assertEquals(isNone(countryCode("US")), true);
  assertEquals(isNone(countryCode("GB")), true);
  assertEquals(isSome(countryCode("USA")), true);
  assertEquals(isSome(countryCode("us")), true);
});

Deno.test("languageCode validation", () => {
  assertEquals(isNone(languageCode("en")), true);
  assertEquals(isNone(languageCode("fr")), true);
  assertEquals(isSome(languageCode("eng")), true);
  assertEquals(isSome(languageCode("EN")), true);
});

Deno.test("priority validation", () => {
  assertEquals(isNone(priority("critical")), true);
  assertEquals(isNone(priority("high")), true);
  assertEquals(isNone(priority("medium")), true);
  assertEquals(isNone(priority("low")), true);
  assertEquals(isSome(priority("invalid" as any)), true);
});

Deno.test("compose validation rules", () => {
  const composedRule = compose(required, minLength(3), maxLength(10));
  
  assertEquals(isNone(composedRule("hello")), true);
  assertEquals(isSome(composedRule("")), true); // fails required
  assertEquals(isSome(composedRule("hi")), true); // fails minLength
  assertEquals(isSome(composedRule("verylongstring")), true); // fails maxLength
});

Deno.test("validateObject - success case", () => {
  const obj = { name: "test", email: "<EMAIL>" };
  const rules = { 
    name: required, 
    email: compose(required, email) 
  };
  
  const result = validateObject(obj, rules);
  assertEquals(hasValidationErrors(result), false);
  assertEquals(getValidationErrors(result), null);
});

Deno.test("validateObject - failure case", () => {
  const obj = { name: "", email: "invalid-email" };
  const rules = { 
    name: required, 
    email: compose(required, email) 
  };
  
  const result = validateObject(obj, rules);
  assertEquals(hasValidationErrors(result), true);
  
  const errors = getValidationErrors(result);
  assertEquals(errors !== null, true);
  if (errors) {
    assertEquals(typeof errors.name, "string");
    assertEquals(typeof errors.email, "string");
  }
});