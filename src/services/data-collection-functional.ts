// Functional Data Collection Service - Pure functions with dependency injection
import type { Result, CountryCode, PipelineStage } from "../types/core.ts";
import { Ok, Err, isOk, collectResults, composePipeline } from "../types/core.ts";
import { match } from "ts-pattern";

// Data source configuration (immutable)
export type DataSource = {
  readonly id: string;
  readonly name: string;
  readonly country_code: CountryCode;
  readonly source_type: "web_scraping" | "api" | "document_feed";
  readonly base_url: string;
  readonly rate_limit_ms: number;
  readonly authentication?: AuthConfig;
  readonly extraction_config: ExtractionConfig;
  readonly active: boolean;
};

// Authentication configuration
export type AuthConfig =
  | { readonly type: "api_key"; readonly key: string; readonly header_name: string }
  | { readonly type: "bearer_token"; readonly token: string }
  | { readonly type: "basic_auth"; readonly username: string; readonly password: string }
  | { readonly type: "oauth2"; readonly client_id: string; readonly client_secret: string };

// Content extraction configuration
export type ExtractionConfig = {
  readonly selectors: Record<string, string>;
  readonly patterns: Record<string, RegExp>;
  readonly document_types: readonly string[];
  readonly language_detection: boolean;
  readonly clean_html: boolean;
};

// Scraping result
export type ScrapingResult = {
  readonly source_id: string;
  readonly url: string;
  readonly content: string;
  readonly metadata: ScrapingMetadata;
  readonly extracted_at: Date;
  readonly success: boolean;
};

// Scraping metadata
export type ScrapingMetadata = {
  readonly title?: string;
  readonly description?: string;
  readonly language?: string;
  readonly last_modified?: Date;
  readonly content_type: string;
  readonly content_length: number;
  readonly response_time_ms: number;
};

// HTTP client dependency
export type HttpClient = {
  readonly fetch: (url: string, options?: RequestInit) => Promise<Response>;
  readonly delay: (ms: number) => Promise<void>;
};

// Rate limiter dependency
export type RateLimiter = {
  readonly checkLimit: (sourceId: string, rateLimitMs: number) => Promise<void>;
};

// Logger dependency
export type Logger = {
  readonly info: (message: string, meta?: Record<string, unknown>) => void;
  readonly error: (message: string, error?: Error) => void;
  readonly warn: (message: string, meta?: Record<string, unknown>) => void;
};

// Dependencies container
export type DataCollectionDeps = {
  readonly httpClient: HttpClient;
  readonly rateLimiter: RateLimiter;
  readonly logger: Logger;
};

// Collection request
export type CollectionRequest = {
  readonly sources: readonly DataSource[];
  readonly options?: CollectionOptions;
};

export type CollectionOptions = {
  readonly parallel?: boolean;
  readonly retryAttempts?: number;
  readonly timeoutMs?: number;
};

// Pure function to collect data from all sources
export const collectFromAllSources = async (
  request: CollectionRequest,
  deps: DataCollectionDeps
): Promise<Result<readonly ScrapingResult[], Error>> => {
  const activeSources = request.sources.filter(s => s.active);
  
  if (activeSources.length === 0) {
    return Ok([]);
  }

  const collectionStrategy = request.options?.parallel 
    ? collectParallel 
    : collectSequential;

  try {
    return await collectionStrategy(activeSources, deps);
  } catch (error) {
    deps.logger.error("Collection failed", error as Error);
    return Err(error as Error);
  }
};

// Parallel collection strategy
const collectParallel = async (
  sources: readonly DataSource[],
  deps: DataCollectionDeps
): Promise<Result<readonly ScrapingResult[], Error>> => {
  const collectionPromises = sources.map(async (source, index) => {
    // Stagger requests to respect rate limits
    await deps.httpClient.delay(source.rate_limit_ms * index);
    return collectFromSource(source, deps);
  });

  const results = await Promise.all(collectionPromises);
  const collectedResults = collectResults(results);
  
  if (!isOk(collectedResults)) {
    const errors = collectedResults.error;
    return Err(new Error(`Collection failed for ${errors.length} sources`));
  }
  
  return Ok(collectedResults.data.flat());
};

// Sequential collection strategy
const collectSequential = async (
  sources: readonly DataSource[],
  deps: DataCollectionDeps
): Promise<Result<readonly ScrapingResult[], Error>> => {
  const results: ScrapingResult[] = [];
  const errors: Error[] = [];

  for (const source of sources) {
    const result = await collectFromSource(source, deps);
    
    if (isOk(result)) {
      results.push(...result.data);
    } else {
      errors.push(result.error);
    }
    
    // Respect rate limits between sources
    await deps.httpClient.delay(source.rate_limit_ms);
  }

  return errors.length === 0 
    ? Ok(results)
    : Err(new Error(`Collection failed for ${errors.length} sources`));
};

// Pure function to collect from a specific data source
export const collectFromSource = async (
  source: DataSource,
  deps: DataCollectionDeps
): Promise<Result<readonly ScrapingResult[], Error>> => {
  try {
    await deps.rateLimiter.checkLimit(source.id, source.rate_limit_ms);
    
    return match(source.source_type)
      .with("web_scraping", () => scrapeWebContent(source, deps))
      .with("api", () => collectFromAPI(source, deps))
      .with("document_feed", () => collectFromDocumentFeed(source, deps))
      .exhaustive();
  } catch (error) {
    deps.logger.error(`Failed to collect from source ${source.id}`, error as Error);
    return Err(error instanceof Error ? error : new Error(String(error)));
  }
};

// Web scraping implementation (pure function)
const scrapeWebContent = async (
  source: DataSource,
  deps: DataCollectionDeps
): Promise<Result<readonly ScrapingResult[], Error>> => {
  const startTime = Date.now();
  
  try {
    const headers = createAuthHeaders(source.authentication);
    const response = await deps.httpClient.fetch(source.base_url, { headers });
    
    if (!response.ok) {
      return Err(new Error(`HTTP ${response.status}: ${response.statusText}`));
    }

    const content = await response.text();
    const responseTime = Date.now() - startTime;
    
    const extractedContent = extractContent(content, source.extraction_config);
    const metadata = createScrapingMetadata(content, response, responseTime, source.extraction_config);

    const result: ScrapingResult = {
      source_id: source.id,
      url: source.base_url,
      content: extractedContent,
      metadata,
      extracted_at: new Date(),
      success: true,
    };

    deps.logger.info(`Successfully scraped ${source.id}`, { 
      contentLength: content.length,
      responseTime 
    });

    return Ok([result]);
  } catch (error) {
    return Err(error instanceof Error ? error : new Error(String(error)));
  }
};

// API data collection (pure function)
const collectFromAPI = async (
  source: DataSource,
  deps: DataCollectionDeps
): Promise<Result<readonly ScrapingResult[], Error>> => {
  const startTime = Date.now();
  
  try {
    const headers = createAuthHeaders(source.authentication);
    const response = await deps.httpClient.fetch(source.base_url, { headers });
    
    if (!response.ok) {
      return Err(new Error(`API Error ${response.status}: ${response.statusText}`));
    }

    const data = await response.json();
    const responseTime = Date.now() - startTime;
    
    const results = processAPIResponse(data, source, responseTime);
    
    deps.logger.info(`Successfully collected from API ${source.id}`, {
      resultsCount: results.length,
      responseTime
    });
    
    return Ok(results);
  } catch (error) {
    return Err(error instanceof Error ? error : new Error(String(error)));
  }
};

// Document feed collection (pure function)
const collectFromDocumentFeed = async (
  source: DataSource,
  deps: DataCollectionDeps
): Promise<Result<readonly ScrapingResult[], Error>> => {
  try {
    const headers = createAuthHeaders(source.authentication);
    const response = await deps.httpClient.fetch(source.base_url, { headers });
    const feedContent = await response.text();
    
    const documentUrls = extractDocumentUrls(feedContent, source.extraction_config);
    const results: ScrapingResult[] = [];
    
    for (const url of documentUrls) {
      const docResult = await fetchDocument(url, source, deps);
      if (isOk(docResult)) {
        results.push(docResult.data);
      }
    }
    
    return Ok(results);
  } catch (error) {
    return Err(error instanceof Error ? error : new Error(String(error)));
  }
};

// Pure helper functions
const createAuthHeaders = (auth?: AuthConfig): HeadersInit => {
  const headers: Record<string, string> = {
    "User-Agent": "WTO-Compliance-Monitor/1.0",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Accept-Encoding": "gzip, deflate",
    "Connection": "keep-alive",
  };

  if (!auth) return headers;

  return match(auth)
    .with({ type: "api_key" }, (config) => ({
      ...headers,
      [config.header_name]: config.key,
    }))
    .with({ type: "bearer_token" }, (config) => ({
      ...headers,
      "Authorization": `Bearer ${config.token}`,
    }))
    .with({ type: "basic_auth" }, (config) => ({
      ...headers,
      "Authorization": `Basic ${btoa(`${config.username}:${config.password}`)}`,
    }))
    .with({ type: "oauth2" }, () => {
      throw new Error("OAuth2 authentication not yet implemented");
    })
    .exhaustive();
};

const extractContent = (html: string, config: ExtractionConfig): string => {
  let content = html;
  
  if (config.clean_html) {
    content = content
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "")
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
      .replace(/<[^>]+>/g, " ")
      .replace(/\s+/g, " ")
      .trim();
  }

  for (const [_key, pattern] of Object.entries(config.patterns)) {
    const matches = content.match(pattern);
    if (matches) {
      content = matches[1] || matches[0];
    }
  }

  return content;
};

const createScrapingMetadata = (
  content: string,
  response: Response,
  responseTime: number,
  config: ExtractionConfig
): ScrapingMetadata => ({
  title: extractTitle(content),
  description: extractDescription(content),
  language: config.language_detection ? detectLanguage(content) : undefined,
  content_type: response.headers.get("content-type") || "text/html",
  content_length: content.length,
  response_time_ms: responseTime,
  last_modified: parseLastModified(response.headers.get("last-modified")),
});

const extractTitle = (html: string): string | undefined => {
  const match = html.match(/<title[^>]*>([^<]+)<\/title>/i);
  return match?.[1]?.trim();
};

const extractDescription = (html: string): string | undefined => {
  const match = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
  return match?.[1]?.trim();
};

const detectLanguage = (content: string): string => {
  if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/i.test(content)) return "fr";
  if (/[äöüß]/i.test(content)) return "de";
  if (/[¿¡ñáéíóúü]/i.test(content)) return "es";
  return "en";
};

const parseLastModified = (lastModified: string | null): Date | undefined => {
  return lastModified ? new Date(lastModified) : undefined;
};

const processAPIResponse = (data: unknown, source: DataSource, responseTime: number): ScrapingResult[] => {
  const results: ScrapingResult[] = [];
  
  if (Array.isArray(data)) {
    for (const item of data) {
      results.push(createScrapingResultFromData(item, source, responseTime));
    }
  } else {
    results.push(createScrapingResultFromData(data, source, responseTime));
  }
  
  return results;
};

const createScrapingResultFromData = (data: unknown, source: DataSource, responseTime: number): ScrapingResult => {
  const content = JSON.stringify(data);
  return {
    source_id: source.id,
    url: source.base_url,
    content,
    metadata: {
      content_type: "application/json",
      content_length: content.length,
      response_time_ms: responseTime,
    },
    extracted_at: new Date(),
    success: true,
  };
};

const extractDocumentUrls = (feedContent: string, _config: ExtractionConfig): string[] => {
  const urls: string[] = [];
  
  const urlPatterns = [
    /<link[^>]*href=["\']([^"']+\.pdf)["\'][^>]*>/gi,
    /<a[^>]*href=["\']([^"']+\.pdf)["\'][^>]*>/gi,
    /<url>([^<]+)<\/url>/gi,
  ];
  
  for (const pattern of urlPatterns) {
    let match;
    while ((match = pattern.exec(feedContent)) !== null) {
      urls.push(match[1]);
    }
  }
  
  return [...new Set(urls)];
};

const fetchDocument = async (
  url: string,
  source: DataSource,
  deps: DataCollectionDeps
): Promise<Result<ScrapingResult, Error>> => {
  try {
    const headers = createAuthHeaders(source.authentication);
    const response = await deps.httpClient.fetch(url, { headers });
    
    if (!response.ok) {
      return Err(new Error(`Failed to fetch document: ${response.status}`));
    }
    
    const content = await response.text();
    
    const result: ScrapingResult = {
      source_id: source.id,
      url,
      content,
      metadata: {
        content_type: response.headers.get("content-type") || "application/octet-stream",
        content_length: content.length,
        response_time_ms: 0,
      },
      extracted_at: new Date(),
      success: true,
    };
    
    return Ok(result);
  } catch (error) {
    return Err(error instanceof Error ? error : new Error(String(error)));
  }
};

// Factory functions for creating data sources
export const createDataSource = (
  id: string,
  name: string,
  countryCode: CountryCode,
  sourceType: DataSource["source_type"],
  baseUrl: string,
  rateLimitMs = 1000,
): DataSource => ({
  id,
  name,
  country_code: countryCode,
  source_type: sourceType,
  base_url: baseUrl,
  rate_limit_ms: rateLimitMs,
  extraction_config: {
    selectors: {},
    patterns: {},
    document_types: ["pdf", "html", "xml"],
    language_detection: true,
    clean_html: true,
  },
  active: true,
});

// Predefined data sources
export const createWTODataSources = (): readonly DataSource[] => [
  createDataSource("us-trade-gov", "US Trade.gov", "US", "web_scraping", "https://www.trade.gov/trade-policy"),
  createDataSource("eu-trade", "EU Trade Policy", "EU", "api", "https://policy.trade.ec.europa.eu/api/regulations"),
  createDataSource("jp-jetro", "Japan JETRO", "JP", "web_scraping", "https://www.jetro.go.jp/en/trade/"),
  createDataSource("cn-mofcom", "China MOFCOM", "CN", "web_scraping", "http://english.mofcom.gov.cn/"),
  createDataSource("uk-trade", "UK Trade Bulletins", "GB", "document_feed", "https://www.gov.uk/trade-bulletins.rss"),
];

// Pipeline stages for processing collected data
export const validateScrapingResult: PipelineStage<ScrapingResult, ScrapingResult> = async (result) => {
  if (!result.content || result.content.length === 0) {
    return Err(new Error("Empty content"));
  }
  
  if (result.metadata.content_length !== result.content.length) {
    return Err(new Error("Content length mismatch"));
  }
  
  return Ok(result);
};

export const enrichScrapingResult: PipelineStage<ScrapingResult, ScrapingResult> = async (result) => {
  // Add enrichment logic here (e.g., language detection, classification)
  return Ok({
    ...result,
    metadata: {
      ...result.metadata,
      // Add enriched metadata
    },
  });
};

// Create a processing pipeline
export const createProcessingPipeline = () => 
  composePipeline(validateScrapingResult, enrichScrapingResult);