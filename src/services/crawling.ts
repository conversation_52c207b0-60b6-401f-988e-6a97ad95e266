// Crawling service for regulation change monitoring
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";
import type {
  FirecrawlService,
  WebSource,
  CrawledPage
} from "./firecrawl.ts";
import type { Regulation, RegulationInput } from "../types/regulation.ts";
import type { Change } from "../types/change.ts";
import { getActiveWebSources, getWebSourceById } from "../data/web-sources.ts";

// Crawl job types
export type CrawlJob = {
  readonly id: string;
  readonly webSourceId: string;
  readonly status: "pending" | "running" | "completed" | "failed";
  readonly firecrawlJobId?: string;
  readonly startedAt?: Date;
  readonly completedAt?: Date;
  readonly pagesFound: number;
  readonly regulationsExtracted: number;
  readonly changesDetected: number;
  readonly error?: string;
};

export type CrawlJobResult = {
  readonly job: CrawlJob;
  readonly regulations: readonly RegulationInput[];
  readonly changes: readonly Change[];
};

// Content analysis types
export type ExtractedRegulation = {
  readonly title: string;
  readonly description: string;
  readonly category: string;
  readonly effectiveDate?: Date;
  readonly sourceUrl: string;
  readonly confidence: number;
};

export type RegulationChange = {
  readonly type: "new" | "modified" | "removed";
  readonly regulation: ExtractedRegulation;
  readonly changeDescription: string;
  readonly confidence: number;
};

// Crawling service interface
export type CrawlingService = {
  readonly startCrawlJob: (webSource: WebSource) => Promise<Result<CrawlJob>>;
  readonly getJobStatus: (jobId: string) => Promise<Result<CrawlJob>>;
  readonly getJobsByStatus: (status: CrawlJob["status"]) => Promise<Result<readonly CrawlJob[]>>;
  readonly processCompletedJob: (jobId: string) => Promise<Result<CrawlJobResult>>;
  readonly scheduleAllJobs: () => Promise<Result<readonly CrawlJob[]>>;
  readonly extractRegulations: (pages: readonly CrawledPage[], source: WebSource) => Promise<Result<readonly ExtractedRegulation[]>>;
  readonly detectChanges: (newRegulations: readonly ExtractedRegulation[], existing: readonly Regulation[]) => Promise<Result<readonly RegulationChange[]>>;
};

// Create crawling service implementation
export const createCrawlingService = (
  firecrawl: FirecrawlService,
  logger: Logger,
  database?: { saveJob: (job: CrawlJob) => Promise<Result<void>>; getJob: (id: string) => Promise<Result<CrawlJob>>; updateJobStatus: (id: string, status: CrawlJob["status"], error?: string) => Promise<Result<void>> }
): CrawlingService => {
  // Fallback to in-memory storage if no database provided
  const jobs = new Map<string, CrawlJob>();

  const service: CrawlingService = {
    startCrawlJob: async (webSource: WebSource) => {
      try {
        logger.info(`Starting crawl job for ${webSource.name}`, {
          webSourceId: webSource.id,
          url: webSource.url
        });

        // Start Firecrawl job with enhanced extraction
        const crawlRequest = {
          url: webSource.url,
          crawlerOptions: webSource.crawlConfig,
          pageOptions: {
            onlyMainContent: true,
            includeHtml: false,
          },
          extractorOptions: {
            mode: "llm-extraction" as const,
            extractionPrompt: createWTORegulationExtractionPrompt(),
            extractionSchema: {
              regulations: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    title: { type: "string", description: "Title of the regulation or trade news" },
                    description: { type: "string", description: "Detailed description of the regulation" },
                    category: { type: "string", description: "Category: tariff, technical_regulation, trade_remedies, etc." },
                    effectiveDate: { type: "string", description: "When the regulation takes effect (ISO date)" },
                    publishedDate: { type: "string", description: "When the regulation was published (ISO date)" },
                    agency: { type: "string", description: "Government agency or department" },
                    country: { type: "string", description: "Country code (US, EU, etc.)" },
                    hsCode: { type: "string", description: "HS code if applicable" },
                    impactLevel: { type: "string", description: "high, medium, low" },
                    regulationType: { type: "string", description: "Type of regulation" },
                    sourceUrl: { type: "string", description: "URL of the source page" },
                    isTradeRelated: { type: "boolean", description: "Whether this is trade-related" }
                  }
                }
              }
            }
          },
        };

        const firecrawlResult = await firecrawl.crawlWebsite(crawlRequest);
        if (!firecrawlResult.success) {
          return Err(firecrawlResult.error);
        }

        const job: CrawlJob = {
          id: crypto.randomUUID(),
          webSourceId: webSource.id,
          status: "running",
          firecrawlJobId: firecrawlResult.data.jobId || "",
          startedAt: new Date(),
          pagesFound: 0,
          regulationsExtracted: 0,
          changesDetected: 0,
        };

        // Save job to database or fallback to memory
        if (database) {
          const saveResult = await database.saveJob(job);
          if (!saveResult.success) {
            logger.error("Failed to save job to database", saveResult.error, { jobId: job.id });
            // Fallback to memory storage
            jobs.set(job.id, job);
          }
        } else {
          jobs.set(job.id, job);
        }

        logger.info(`Crawl job started`, {
          jobId: job.id,
          firecrawlJobId: job.firecrawlJobId
        });

        return Ok(job);
      } catch (error) {
        logger.error("Failed to start crawl job", error as Error, {
          webSourceId: webSource.id
        });
        return Err(error as Error);
      }
    },

    getJobStatus: async (jobId: string) => {
      // Try database first, fallback to memory
      let job: CrawlJob | undefined;

      if (database) {
        const dbResult = await database.getJob(jobId);
        if (dbResult.success) {
          job = dbResult.data;
        }
      }

      if (!job) {
        job = jobs.get(jobId);
      }

      if (!job) {
        return Err(new Error(`Job not found: ${jobId}`));
      }

      // If job is still running, check Firecrawl status
      if (job.status === "running" && job.firecrawlJobId) {
        const statusResult = await firecrawl.getJobStatus(job.firecrawlJobId);
        if (statusResult.success) {
          const firecrawlStatus = statusResult.data;

          const updatedJob: CrawlJob = {
            ...job,
            status: firecrawlStatus.status === "completed" ? "completed" :
              firecrawlStatus.status === "failed" ? "failed" : "running",
            pagesFound: firecrawlStatus.total || job.pagesFound,
            completedAt: (firecrawlStatus.status === "completed" || firecrawlStatus.status === "failed")
              ? new Date() : (job.completedAt || new Date()),
          };

          jobs.set(jobId, updatedJob);
          return Ok(updatedJob);
        }
      }

      return Ok(job);
    },

    getJobsByStatus: async (status: CrawlJob["status"]) => {
      try {
        // Get jobs from database if available
        if (database) {
          // TODO: Implement database query for jobs by status
          // For now, fall back to in-memory storage
          logger.debug("Database query for jobs by status not implemented, using in-memory storage");
        }

        // Filter in-memory jobs by status
        const jobsWithStatus = Array.from(jobs.values()).filter(job => job.status === status);

        logger.debug(`Found ${jobsWithStatus.length} jobs with status: ${status}`);
        return Ok(jobsWithStatus);
      } catch (error) {
        logger.error("Failed to get jobs by status", error as Error, { status });
        return Err(error as Error);
      }
    },

    processCompletedJob: async (jobId: string) => {
      const job = jobs.get(jobId);
      if (!job) {
        return Err(new Error(`Job not found: ${jobId}`));
      }

      if (job.status !== "completed" || !job.firecrawlJobId) {
        return Err(new Error(`Job is not completed: ${job.status}`));
      }

      try {
        logger.info(`Processing completed job`, { jobId });

        // Get crawled data from Firecrawl
        const statusResult = await firecrawl.getJobStatus(job.firecrawlJobId);
        if (!statusResult.success || !statusResult.data.data) {
          return Err(new Error("Failed to get crawled data"));
        }

        const pages = statusResult.data.data;

        // TODO: Get web source configuration
        const webSource = getWebSourceById(job.webSourceId);
        if (!webSource) {
          return Err(new Error(`Web source not found: ${job.webSourceId}`));
        }

        // First try to use Firecrawl's extracted data, then fallback to our extraction
        let extractedRegulations: ExtractedRegulation[] = [];

        // Check if Firecrawl extracted structured data
        const hasFirecrawlExtraction = pages.some(page =>
          page.extract && typeof page.extract === 'object' && 'regulations' in page.extract
        );

        if (hasFirecrawlExtraction) {
          logger.info("Using Firecrawl extracted data");
          for (const page of pages) {
            if (page.extract && typeof page.extract === 'object' && 'regulations' in page.extract) {
              const firecrawlRegulations = (page.extract as Record<string, unknown>)["regulations"];
              if (Array.isArray(firecrawlRegulations)) {
                for (const reg of firecrawlRegulations) {
                  if (reg.isTradeRelated) {
                    extractedRegulations.push({
                      title: reg.title || "Untitled Regulation",
                      description: reg.description || "No description available",
                      category: reg.category || "technical_regulation",
                      effectiveDate: reg.effectiveDate ? new Date(reg.effectiveDate) : new Date(),
                      sourceUrl: reg.sourceUrl || page.url,
                      confidence: reg.impactLevel === "high" ? 0.9 : reg.impactLevel === "medium" ? 0.7 : 0.5,
                    });
                  }
                }
              }
            }
          }
        } else {
          // Fallback to our basic extraction
          logger.info("Using fallback extraction method");
          const extractResult = await service.extractRegulations(pages, webSource);
          if (!extractResult.success) {
            return Err(extractResult.error);
          }
          extractedRegulations = [...extractResult.data];
        }

        // Convert to regulation inputs
        const regulations: RegulationInput[] = extractedRegulations.map(reg => ({
          country_code: webSource.country,
          source_agency: webSource.agency,
          title: { en: reg.title },
          description: { en: reg.description },
          category: (reg.category as "technical_regulation" | "tariff" | "trade_remedies" | "sanitary_phytosanitary" | "non_tariff_barrier" | "services_regulation" | "intellectual_property" | "government_procurement" | "customs_procedure" | "trade_facilitation") || "technical_regulation",
          subcategory: "general",
          hs_codes: [],
          timeline: {
            effective_date: reg.effectiveDate || new Date(),
          },
          impact_assessment: {
            economic: Math.round(reg.confidence * 10),
            operational: 5,
            compliance: 7,
            urgency: 5,
          },
          related_regulations: [],
          original_language: "en",
          document_metadata: {
            source_url: reg.sourceUrl,
            document_hash: crypto.randomUUID(),
            content_type: "text/html",
            file_size: reg.description.length,
            extracted_at: new Date(),
          },
        }));

        // TODO: Detect changes against existing regulations
        const changes: Change[] = [];

        const result: CrawlJobResult = {
          job: {
            ...job,
            regulationsExtracted: regulations.length,
            changesDetected: changes.length,
          },
          regulations,
          changes,
        };

        jobs.set(jobId, result.job);

        logger.info(`Job processing completed`, {
          jobId,
          regulationsExtracted: regulations.length,
          changesDetected: changes.length,
        });

        return Ok(result);
      } catch (error) {
        logger.error("Failed to process completed job", error as Error, { jobId });

        const failedJob = {
          ...job,
          status: "failed" as const,
          error: (error as Error).message,
          completedAt: new Date(),
        };

        jobs.set(jobId, failedJob);
        return Err(error as Error);
      }
    },

    scheduleAllJobs: async () => {
      logger.info("Scheduling crawl jobs for all active web sources");

      const sources = getActiveWebSources();
      const results: CrawlJob[] = [];

      for (const source of sources) {
        const result = await service.startCrawlJob(source);
        if (result.success) {
          results.push(result.data);
        } else {
          logger.error(`Failed to start job for ${source.name}`, result.error);
        }
      }

      return Ok(results);
    },

    extractRegulations: async (pages: readonly CrawledPage[], source: WebSource) => {
      logger.info(`Extracting regulations from ${pages.length} pages`, {
        webSourceId: source.id
      });

      const regulations: ExtractedRegulation[] = [];

      for (const page of pages) {
        const content = page.markdown.toLowerCase();
        const originalContent = page.markdown;

        // Enhanced trade regulation detection
        const tradeKeywords = [
          // Core trade terms
          'trade policy', 'trade agreement', 'trade deal', 'trade negotiation',
          'tariff', 'duty', 'customs', 'import', 'export', 'quota',

          // Regulatory terms
          'regulation', 'rule', 'directive', 'measure', 'policy', 'procedure',
          'requirement', 'standard', 'specification', 'compliance',

          // Trade barriers
          'anti-dumping', 'countervailing', 'safeguard', 'trade remedy',
          'technical barrier', 'sanitary', 'phytosanitary', 'sps', 'tbt',

          // Trade facilitation
          'trade facilitation', 'customs procedure', 'border control',
          'certificate', 'license', 'permit', 'authorization',

          // International trade
          'wto', 'world trade organization', 'gatt', 'nafta', 'usmca',
          'free trade', 'preferential trade', 'most favored nation', 'mfn',

          // Economic terms
          'economic partnership', 'bilateral', 'multilateral', 'regional trade',
          'supply chain', 'value chain', 'rules of origin'
        ];

        // Calculate keyword density and relevance
        const keywordMatches = tradeKeywords.filter(keyword => content.includes(keyword));
        const keywordDensity = keywordMatches.length / tradeKeywords.length;

        // Enhanced content analysis
        const hasTradeContent = keywordDensity > 0.02 || // At least 2% keyword match
          keywordMatches.some(keyword => ['tariff', 'trade policy', 'regulation', 'wto'].includes(keyword));

        // Look for specific trade regulation patterns
        const regulationPatterns = [
          /new\s+(regulation|rule|policy|measure)/gi,
          /updated?\s+(regulation|rule|policy|measure)/gi,
          /revised?\s+(regulation|rule|policy|measure)/gi,
          /announced?\s+(regulation|rule|policy|measure)/gi,
          /effective\s+date/gi,
          /implementation\s+date/gi,
          /tariff\s+(rate|schedule|classification)/gi,
          /hs\s+code/gi,
          /harmonized\s+system/gi,
          /trade\s+(agreement|deal|negotiation)/gi,
          /anti-dumping\s+(duty|measure)/gi,
          /countervailing\s+(duty|measure)/gi,
          /safeguard\s+measure/gi,
          /technical\s+barrier/gi,
          /sanitary\s+and\s+phytosanitary/gi,
          /rules\s+of\s+origin/gi,
          /certificate\s+of\s+origin/gi,
          /export\s+(control|restriction|license)/gi,
          /import\s+(requirement|restriction|license)/gi
        ];

        const patternMatches = regulationPatterns.filter(pattern => pattern.test(originalContent));
        const hasRegulationPatterns = patternMatches.length > 0;

        // Enhanced title and content analysis
        const title = page.metadata.title || "";
        const titleLower = title.toLowerCase();

        const hasTitleIndicators = [
          'news', 'announcement', 'update', 'notice', 'alert',
          'regulation', 'policy', 'measure', 'directive', 'rule',
          'trade', 'tariff', 'customs', 'import', 'export'
        ].some(indicator => titleLower.includes(indicator));

        // Check for date indicators (recent content is more likely to be relevant)
        const hasRecentDate = page.metadata.publishedDate ||
          extractDateFromContent(originalContent);

        // Calculate overall relevance score
        const relevanceScore =
          (hasTradeContent ? 0.4 : 0) +
          (hasRegulationPatterns ? 0.3 : 0) +
          (hasTitleIndicators ? 0.2 : 0) +
          (hasRecentDate ? 0.1 : 0);

        // Only extract if relevance score is above threshold
        if (relevanceScore > 0.3 && title) {
          // Extract more detailed information
          const extractedTitle = enhanceTitle(title, keywordMatches);
          const extractedDescription = enhanceDescription(
            page.metadata.description || originalContent.substring(0, 1000),
            keywordMatches,
            patternMatches
          );

          const regulation: ExtractedRegulation = {
            title: extractedTitle,
            description: extractedDescription,
            category: determineCategoryFromContent(content, source.category, keywordMatches),
            effectiveDate: extractDateFromContent(originalContent) || new Date(),
            sourceUrl: page.url,
            confidence: Math.min(relevanceScore + calculateConfidence(page, source), 1.0),
          };

          regulations.push(regulation);

          logger.debug("Extracted regulation", {
            title: extractedTitle,
            relevanceScore,
            keywordMatches: keywordMatches.length,
            patternMatches: patternMatches.length
          });
        }
      }

      logger.info(`Extracted ${regulations.length} potential regulations from ${pages.length} pages`, {
        webSourceId: source.id,
        averageConfidence: regulations.length > 0
          ? regulations.reduce((sum, reg) => sum + reg.confidence, 0) / regulations.length
          : 0
      });

      return Ok(regulations);
    },

    detectChanges: async (newRegulations: readonly ExtractedRegulation[], existing: readonly Regulation[]) => {
      logger.info(`Detecting changes between ${newRegulations.length} new and ${existing.length} existing regulations`);

      const changes: RegulationChange[] = [];

      // Simple change detection based on title similarity
      for (const newReg of newRegulations) {
        const existingMatch = existing.find(existing =>
          calculateSimilarity(newReg.title, existing.title["en"] || "") > 0.8
        );

        if (!existingMatch) {
          // New regulation
          changes.push({
            type: "new",
            regulation: newReg,
            changeDescription: `New regulation detected: ${newReg.title}`,
            confidence: newReg.confidence,
          });
        }
        // TODO: Add logic for modified regulations
      }

      return Ok(changes);
    },
  };

  return service;
};

// WTO Regulation Extraction Prompt
const createWTORegulationExtractionPrompt = (): string => {
  return `
You are an expert trade regulation analyst specializing in WTO compliance and international trade law.
Your task is to extract structured information about trade regulations, policy updates, and trade-related news from web content.

EXTRACTION CRITERIA:
1. Focus on content related to:
   - Trade regulations and policies
   - Tariff changes and updates
   - Import/export requirements
   - Technical barriers to trade (TBT)
   - Sanitary and phytosanitary (SPS) measures
   - Trade remedies (anti-dumping, countervailing duties)
   - Trade agreements and negotiations
   - Customs procedures and requirements
   - Export controls and licensing
   - Trade enforcement actions

2. For each relevant item found, extract:
   - Title: Clear, descriptive title of the regulation/news
   - Description: Comprehensive summary of the content and implications
   - Category: Classify as tariff, technical_regulation, trade_remedies, sanitary_phytosanitary, export_control, or trade_agreement
   - Effective Date: When the regulation takes effect (if mentioned)
   - Published Date: When the regulation was announced/published
   - Agency: Government department or agency responsible
   - Country: Country or region this applies to
   - HS Code: Harmonized System codes if mentioned
   - Impact Level: Assess as high, medium, or low based on scope and significance
   - Regulation Type: Type of measure (regulation, directive, notice, etc.)
   - Source URL: URL of the source page
   - Is Trade Related: Boolean indicating if this is directly trade-related

3. QUALITY STANDARDS:
   - Only extract content that is clearly trade-related
   - Prioritize recent announcements and updates
   - Include both new regulations and modifications to existing ones
   - Exclude general news unless it has specific trade implications
   - Focus on actionable information for trade compliance

4. OUTPUT FORMAT:
   Return a JSON object with a "regulations" array containing all extracted items.
   If no trade-related content is found, return an empty array.

IMPORTANT: Be thorough but selective. Quality over quantity. Focus on content that would be relevant for international trade compliance monitoring.
`;
};

// Enhanced helper functions for trade regulation extraction

const enhanceTitle = (title: string, keywordMatches: string[]): string => {
  // Clean up title and add context if needed
  let enhancedTitle = title.trim();

  // If title is too generic, try to add context from keywords
  if (enhancedTitle.toLowerCase().includes('news') && keywordMatches.length > 0) {
    const topKeyword = keywordMatches[0];
    if (topKeyword) {
      enhancedTitle = `${enhancedTitle} - ${topKeyword.charAt(0).toUpperCase() + topKeyword.slice(1)}`;
    }
  }

  return enhancedTitle;
};

const enhanceDescription = (description: string, keywordMatches: string[], _patternMatches: RegExp[]): string => {
  let enhancedDescription = description.trim();

  // Truncate if too long
  if (enhancedDescription.length > 1000) {
    enhancedDescription = enhancedDescription.substring(0, 1000) + "...";
  }

  // Add context about trade relevance
  if (keywordMatches.length > 0) {
    const relevantKeywords = keywordMatches.slice(0, 3).join(", ");
    enhancedDescription += ` [Trade-related keywords: ${relevantKeywords}]`;
  }

  return enhancedDescription;
};

const determineCategoryFromContent = (_content: string, sourceCategories: string[], keywordMatches: string[]): string => {
  // Enhanced category determination based on content analysis

  // Check for specific category indicators
  if (keywordMatches.some(k => k.includes('tariff') || k.includes('duty'))) {
    return 'tariff';
  }

  if (keywordMatches.some(k => k.includes('anti-dumping') || k.includes('countervailing') || k.includes('safeguard'))) {
    return 'trade_remedies';
  }

  if (keywordMatches.some(k => k.includes('sanitary') || k.includes('phytosanitary') || k.includes('sps'))) {
    return 'sanitary_phytosanitary';
  }

  if (keywordMatches.some(k => k.includes('technical barrier') || k.includes('tbt') || k.includes('standard'))) {
    return 'technical_regulation';
  }

  if (keywordMatches.some(k => k.includes('customs') || k.includes('border') || k.includes('procedure'))) {
    return 'customs_procedure';
  }

  if (keywordMatches.some(k => k.includes('trade facilitation') || k.includes('facilitation'))) {
    return 'trade_facilitation';
  }

  if (keywordMatches.some(k => k.includes('service') || k.includes('services'))) {
    return 'services_regulation';
  }

  if (keywordMatches.some(k => k.includes('intellectual property') || k.includes('patent') || k.includes('trademark'))) {
    return 'intellectual_property';
  }

  if (keywordMatches.some(k => k.includes('procurement') || k.includes('government procurement'))) {
    return 'government_procurement';
  }

  // Fallback to source categories or default
  return sourceCategories[0] || 'technical_regulation';
};

const extractDateFromContent = (content: string): Date | undefined => {
  // Simple date extraction (in production, use proper date parsing)
  const dateRegex = /(\d{4}-\d{2}-\d{2})|(\d{1,2}\/\d{1,2}\/\d{4})|(\w+ \d{1,2}, \d{4})/;
  const match = content.match(dateRegex);

  if (match) {
    const date = new Date(match[0]);
    return isNaN(date.getTime()) ? undefined : date;
  }

  return undefined;
};

const calculateConfidence = (page: CrawledPage, source: WebSource): number => {
  let confidence = 0.5; // Base confidence

  // Higher confidence for official government sources
  if (source.url.includes('.gov') || source.url.includes('.europa.eu')) {
    confidence += 0.3;
  }

  // Higher confidence for pages with good metadata
  if (page.metadata.title && page.metadata.description) {
    confidence += 0.1;
  }

  // Higher confidence for recent content
  if (page.metadata.publishedDate) {
    const publishDate = new Date(page.metadata.publishedDate);
    const daysSincePublish = (Date.now() - publishDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSincePublish < 30) {
      confidence += 0.1;
    }
  }

  return Math.min(confidence, 1.0);
};

const calculateSimilarity = (str1: string, str2: string): number => {
  // Simple similarity calculation (in production, use proper text similarity)
  const words1 = str1.toLowerCase().split(/\s+/);
  const words2 = str2.toLowerCase().split(/\s+/);

  const commonWords = words1.filter(word => words2.includes(word));
  const totalWords = new Set([...words1, ...words2]).size;

  return commonWords.length / totalWords;
};

// Logger interface (should match existing logger)
type Logger = {
  readonly info: (message: string, meta?: Record<string, unknown>) => void;
  readonly error: (message: string, error?: Error, meta?: Record<string, unknown>) => void;
  readonly warn: (message: string, meta?: Record<string, unknown>) => void;
  readonly debug: (message: string, meta?: Record<string, unknown>) => void;
};