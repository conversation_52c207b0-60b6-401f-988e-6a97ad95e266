// Mock Firecrawl service for testing crawl pipeline
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";
import type { FirecrawlService, CrawlResult, JobStatus, CrawledPage } from "./firecrawl.ts";

// Mock Firecrawl service implementation
export const createMockFirecrawlService = (): FirecrawlService => {
  const mockJobs = new Map<string, { 
    status: "scraping" | "completed" | "failed"; 
    data?: CrawledPage[];
    startTime: number;
  }>();
  
  return {
    scrapeUrl: async (url: string) => {
      console.log(`🔥 Mock scraping: ${url}`);
      
      const mockPage: CrawledPage = {
        url,
        markdown: generateMockContent(url),
        metadata: {
          title: "Trade Regulation Update",
          description: "Latest trade regulation and policy updates",
          publishedDate: new Date().toISOString(),
        },
        extract: {
          regulations: [{
            title: "Mock Trade Regulation",
            description: "A mock regulation for testing purposes",
            category: "technical_regulation",
            effectiveDate: "2024-01-01",
            agency: "Department of Commerce",
            country: "US",
            hsCode: "8523",
            impactLevel: "medium",
            regulationType: "technical_barrier",
            sourceUrl: url,
            isTradeRelated: true
          }]
        }
      };
      
      return Ok(mockPage);
    },

    crawlWebsite: async (request) => {
      console.log(`🔥 Mock crawling: ${request.url}`);
      const jobId = `mock-job-${Date.now()}`;
      
      // Simulate job creation
      mockJobs.set(jobId, { 
        status: "scraping",
        startTime: Date.now()
      });
      
      // Simulate completion after a delay
      setTimeout(() => {
        const mockPages: CrawledPage[] = generateMockPages(request.url, request.crawlerOptions?.limit || 5);
        mockJobs.set(jobId, { 
          status: "completed", 
          data: mockPages,
          startTime: Date.now()
        });
        console.log(`✅ Mock job ${jobId} completed with ${mockPages.length} pages`);
      }, 3000); // Complete after 3 seconds
      
      const result: CrawlResult = {
        success: true,
        jobId,
      };
      
      return Ok(result);
    },

    getJobStatus: async (jobId: string) => {
      const job = mockJobs.get(jobId);
      
      if (!job) {
        return Err(new Error(`Job not found: ${jobId}`));
      }
      
      const status: JobStatus = {
        status: job.status,
        current: job.status === "completed" ? (job.data?.length || 0) : 1,
        total: job.data?.length || 5,
        data: job.data,
      };
      
      return Ok(status);
    },

    cancelJob: async (jobId: string) => {
      mockJobs.delete(jobId);
      return Ok(undefined);
    },
  };
};

// Generate mock content for different URLs
const generateMockContent = (url: string): string => {
  if (url.includes("trade.gov")) {
    return `# US Trade Policy Update

## New Electronics Tariff Rates

The Department of Commerce announces updated tariff rates for electronic goods effective March 1, 2024.

### Affected Products:
- Computers and peripherals (HS Code: 8471)
- Electronic components (HS Code: 8473)
- Semiconductors (HS Code: 8542)

### New Rates:
- Standard rate: 15% ad valorem
- Preferential rate: 5% for qualifying countries

### Implementation Timeline:
- Effective Date: March 1, 2024
- Comment period ends: February 15, 2024

This regulation implements technical barriers to trade measures in accordance with WTO TBT Agreement.`;
  }
  
  if (url.includes("wto.org")) {
    return `# WTO Trade Facilitation Update

## New Digital Services Agreement

The World Trade Organization announces progress on digital services trade facilitation measures.

### Key Provisions:
- Cross-border data flows
- Digital payment systems
- E-commerce regulations
- Cybersecurity standards

### Affected Sectors:
- Financial services
- Telecommunications
- Software and IT services
- Digital content

### Timeline:
- Negotiation completion: Q2 2024
- Implementation: Q4 2024

This agreement aims to reduce technical barriers and facilitate international digital trade.`;
  }
  
  return `# Trade Regulation News

New trade measures announced affecting international commerce.

## Key Points:
- Updated compliance requirements
- New certification procedures
- Modified inspection protocols
- Enhanced documentation standards

Effective date: ${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}`;
};

// Generate multiple mock pages
const generateMockPages = (baseUrl: string, count: number): CrawledPage[] => {
  const pages: CrawledPage[] = [];
  
  for (let i = 1; i <= count; i++) {
    const url = `${baseUrl}/article-${i}`;
    const topics = [
      "Tariff Updates",
      "Anti-Dumping Measures", 
      "Technical Barriers",
      "Trade Agreements",
      "Export Controls"
    ];
    
    const categories = [
      "tariff",
      "trade_remedies", 
      "technical_regulation",
      "trade_facilitation",
      "export_control"
    ];
    
    const topic = topics[i % topics.length];
    const category = categories[i % categories.length];
    
    pages.push({
      url,
      markdown: `# ${topic}\n\nLatest updates on ${topic.toLowerCase()} affecting international trade.\n\nEffective Date: ${new Date(Date.now() + i * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}\n\nThis regulation affects multiple HS codes and requires compliance by importers and exporters.`,
      metadata: {
        title: `${topic} - Trade Update ${i}`,
        description: `Information about ${topic.toLowerCase()} in international trade`,
        publishedDate: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
      },
      extract: {
        regulations: [{
          title: `${topic} Regulation ${i}`,
          description: `Updated ${topic.toLowerCase()} requirements for international trade`,
          category,
          effectiveDate: new Date(Date.now() + i * 7 * 24 * 60 * 60 * 1000).toISOString(),
          agency: baseUrl.includes("trade.gov") ? "Department of Commerce" : "World Trade Organization",
          country: baseUrl.includes("trade.gov") ? "US" : "GLOBAL",
          hsCode: `${8400 + i}`,
          impactLevel: i % 3 === 0 ? "high" : i % 2 === 0 ? "medium" : "low",
          regulationType: category,
          sourceUrl: url,
          isTradeRelated: true
        }]
      }
    });
  }
  
  return pages;
};
