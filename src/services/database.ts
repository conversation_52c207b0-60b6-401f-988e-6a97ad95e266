// Database Service - Functional Implementation with KV and File Storage Fallback
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";
import type { Regulation, RegulationInput } from "../types/regulation.ts";
import type { Change } from "../types/change.ts";
import type { CrawlJob, CrawlJobResult } from "./crawling.ts";
import type { WebSource } from "./firecrawl.ts";
import { createFileStorage, defaultFileStorageConfig, type FileStorage } from "./file-storage.ts";

// Database configuration
type DatabaseConfig = {
  readonly kvPath?: string;
  readonly enableConsistency?: boolean;
  readonly fileStorageDir?: string;
  readonly preferFileStorage?: boolean;
};

// Database service interface
export type DatabaseService = {
  // Job management
  readonly saveJob: (job: CrawlJob) => Promise<Result<void>>;
  readonly getJob: (jobId: string) => Promise<Result<CrawlJob>>;
  readonly updateJobStatus: (jobId: string, status: CrawlJob["status"], error?: string) => Promise<Result<void>>;
  readonly getJobsByStatus: (status: CrawlJob["status"]) => Promise<Result<readonly CrawlJob[]>>;
  readonly getAllJobs: (limit?: number) => Promise<Result<readonly CrawlJob[]>>;
  readonly deleteJob: (jobId: string) => Promise<Result<void>>;

  // Job results management
  readonly saveJobResult: (result: CrawlJobResult) => Promise<Result<void>>;
  readonly getJobResult: (jobId: string) => Promise<Result<CrawlJobResult>>;

  // Regulation management
  readonly saveRegulation: (regulation: RegulationInput) => Promise<Result<string>>;
  readonly getRegulation: (id: string) => Promise<Result<Regulation>>;
  readonly findRegulations: (filters: RegulationFilters) => Promise<Result<readonly Regulation[]>>;
  readonly updateRegulation: (id: string, updates: Partial<RegulationInput>) => Promise<Result<void>>;
  readonly deleteRegulation: (id: string) => Promise<Result<void>>;

  // Change management
  readonly saveChange: (change: Change) => Promise<Result<string>>;
  readonly getChange: (id: string) => Promise<Result<Change>>;
  readonly getChangesByRegulation: (regulationId: string) => Promise<Result<readonly Change[]>>;
  readonly getRecentChanges: (limit?: number) => Promise<Result<readonly Change[]>>;

  // Web source management
  readonly saveWebSource: (source: WebSource) => Promise<Result<void>>;
  readonly getWebSource: (id: string) => Promise<Result<WebSource>>;
  readonly getActiveWebSources: () => Promise<Result<readonly WebSource[]>>;
  readonly updateWebSource: (id: string, updates: Partial<WebSource>) => Promise<Result<void>>;
  readonly deleteWebSource: (id: string) => Promise<Result<void>>;

  // Utility
  readonly close: () => Promise<void>;
  readonly clear: () => Promise<Result<void>>;
};

// Filter types
type RegulationFilters = {
  readonly country?: string;
  readonly agency?: string;
  readonly category?: string;
  readonly fromDate?: Date;
  readonly toDate?: Date;
  readonly limit?: number;
};

// KV key patterns
const Keys = {
  job: (id: string) => ["jobs", id],
  jobsByStatus: (status: string) => ["jobs_by_status", status],
  jobResult: (jobId: string) => ["job_results", jobId],
  regulation: (id: string) => ["regulations", id],
  regulationsByCountry: (country: string) => ["regulations_by_country", country],
  regulationsByAgency: (agency: string) => ["regulations_by_agency", agency],
  regulationsByCategory: (category: string) => ["regulations_by_category", category],
  change: (id: string) => ["changes", id],
  changesByRegulation: (regulationId: string) => ["changes_by_regulation", regulationId],
  webSource: (id: string) => ["web_sources", id],
  activeWebSources: () => ["active_web_sources"],
  counters: () => ["counters"],
} as const;

// Logger interface
type Logger = {
  readonly info: (message: string, meta?: Record<string, unknown>) => void;
  readonly error: (message: string, error?: Error, meta?: Record<string, unknown>) => void;
  readonly warn: (message: string, meta?: Record<string, unknown>) => void;
  readonly debug: (message: string, meta?: Record<string, unknown>) => void;
};

// Storage abstraction - works with both KV and File Storage
type StorageAdapter = {
  readonly get: <T>(key: readonly string[]) => Promise<{ value: T | null }>;
  readonly set: (key: readonly string[], value: unknown) => Promise<void>;
  readonly delete: (key: readonly string[]) => Promise<void>;
  readonly list: <T>(prefix: { prefix: readonly string[] }, options?: { limit?: number }) => AsyncIterable<{ key: readonly string[]; value: T }>;
  readonly atomic: () => StorageAtomic;
  readonly close: () => Promise<void>;
  readonly clear: () => Promise<Result<void>>;
};

type StorageAtomic = {
  readonly set: (key: readonly string[], value: unknown) => StorageAtomic;
  readonly delete: (key: readonly string[]) => StorageAtomic;
  readonly commit: () => Promise<{ ok: boolean; error?: string }>;
};

// Create database service
export const createDatabaseService = async (
  config: DatabaseConfig = {},
  logger: Logger
): Promise<Result<DatabaseService>> => {
  try {
    let storage: StorageAdapter;
    let storageType: string;

    // Try to use Deno KV first, fallback to file storage
    const useFileStorage = config.preferFileStorage || typeof Deno?.openKv !== "function";
    
    if (useFileStorage) {
      logger.info("Using file storage for database");
      const fileStorageResult = await createFileStorage({
        storageDir: config.fileStorageDir || defaultFileStorageConfig.storageDir,
        enableIndexes: true,
      }, logger);
      
      if (!fileStorageResult.success) {
        return fileStorageResult as Result<DatabaseService>;
      }
      
      const fileStorage = fileStorageResult.data;
      storage = {
        get: async <T>(key: readonly string[]) => {
          const result = await fileStorage.get<T>(key);
          return { value: result.success ? result.data : null };
        },
        set: async (key: readonly string[], value: unknown) => {
          const result = await fileStorage.set(key, value);
          if (!result.success) throw result.error;
        },
        delete: async (key: readonly string[]) => {
          const result = await fileStorage.delete(key);
          if (!result.success) throw result.error;
        },
        list: fileStorage.list,
        atomic: () => {
          const fileAtomic = fileStorage.atomic();
          return {
            set: (key: readonly string[], value: unknown) => {
              fileAtomic.set(key, value);
              return storage.atomic();
            },
            delete: (key: readonly string[]) => {
              fileAtomic.delete(key);
              return storage.atomic();
            },
            commit: fileAtomic.commit,
          };
        },
        close: fileStorage.close,
        clear: fileStorage.clear,
      };
      storageType = "file";
    } else {
      logger.info("Using Deno KV for database");
      const kv = await Deno.openKv(config.kvPath);
      
      storage = {
        get: <T>(key: readonly string[]) => kv.get<T>(key),
        set: async (key: readonly string[], value: unknown) => {
          await storage.set(key, value);
        },
        delete: async (key: readonly string[]) => {
          await kv.delete(key);
        },
        list: <T>(prefix: { prefix: readonly string[] }, options?: { limit?: number }) => 
          kv.list<T>(prefix, options),
        atomic: () => {
          const kvAtomic = kv.atomic();
          return {
            set: (key: readonly string[], value: unknown) => {
              kvAtomic.set(key, value);
              return storage.atomic();
            },
            delete: (key: readonly string[]) => {
              kvAtomic.delete(key);
              return storage.atomic();
            },
            commit: kvAtomic.commit,
          };
        },
        close: async () => kv.close(),
        clear: async () => {
          // Clear all data (be careful with this!)
          const keys: string[][] = [];
          for await (const entry of kv.list({ prefix: [] })) {
            keys.push(entry.key as string[]);
          }
          const atomic = storage.atomic();
          for (const key of keys) {
            atomic.delete(key);
          }
          const result = await atomic.commit();
          return result.ok ? Ok(undefined) : Err(new Error("Failed to clear KV store"));
        },
      };
      storageType = "kv";
    }

    logger.info("Database service initialized", {
      storageType,
      kvPath: config.kvPath || "default",
      fileStorageDir: config.fileStorageDir || defaultFileStorageConfig.storageDir,
      enableConsistency: config.enableConsistency
    });

    const service: DatabaseService = {
      // Job management
      saveJob: async (job: CrawlJob) => {
        try {
          const atomic = storage.atomic();

          // Save job
          atomic.set(Keys.job(job.id), job);

          // Index by status
          atomic.set([...Keys.jobsByStatus(job.status), job.id], job.id);

          const result = await atomic.commit();

          if (!result.ok) {
            return Err(new Error("Failed to save job: transaction failed"));
          }

          logger.debug("Job saved", { jobId: job.id, status: job.status });
          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to save job", error as Error, { jobId: job.id });
          return Err(error as Error);
        }
      },

      getJob: async (jobId: string) => {
        try {
          const result = await storage.get<CrawlJob>(Keys.job(jobId));

          if (!result.value) {
            return Err(new Error(`Job not found: ${jobId}`));
          }

          return Ok(result.value);
        } catch (error) {
          logger.error("Failed to get job", error as Error, { jobId });
          return Err(error as Error);
        }
      },

      updateJobStatus: async (jobId: string, status: CrawlJob["status"], error?: string) => {
        try {
          const jobResult = await service.getJob(jobId);

          if (!jobResult.success) {
            return jobResult as Result<void>;
          }

          const job = jobResult.data;
          const updatedJob: CrawlJob = {
            ...job,
            status,
            error,
            completedAt: (status === "completed" || status === "failed") ? new Date() : job.completedAt,
          };

          const atomic = storage.atomic();

          // Remove from old status index
          atomic.delete([...Keys.jobsByStatus(job.status), jobId]);

          // Update job
          atomic.set(Keys.job(jobId), updatedJob);

          // Add to new status index
          atomic.set([...Keys.jobsByStatus(status), jobId], jobId);

          const result = await atomic.commit();

          if (!result.ok) {
            return Err(new Error("Failed to update job status: transaction failed"));
          }

          logger.debug("Job status updated", { jobId, oldStatus: job.status, newStatus: status });
          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to update job status", error as Error, { jobId, status });
          return Err(error as Error);
        }
      },

      getJobsByStatus: async (status: CrawlJob["status"]) => {
        try {
          const jobs: CrawlJob[] = [];
          const iter = storage.list<string>({ prefix: Keys.jobsByStatus(status) });

          for await (const entry of iter) {
            const jobId = entry.value;
            const jobResult = await service.getJob(jobId);

            if (jobResult.success) {
              jobs.push(jobResult.data);
            }
          }

          return Ok(jobs);
        } catch (error) {
          logger.error("Failed to get jobs by status", error as Error, { status });
          return Err(error as Error);
        }
      },

      getAllJobs: async (limit = 100) => {
        try {
          const jobs: CrawlJob[] = [];
          const iter = storage.list<CrawlJob>({ prefix: ["jobs"] }, { limit });

          for await (const entry of iter) {
            if (entry.value && typeof entry.value === "object" && "id" in entry.value) {
              jobs.push(entry.value);
            }
          }

          return Ok(jobs);
        } catch (error) {
          logger.error("Failed to get all jobs", error as Error, { limit });
          return Err(error as Error);
        }
      },

      deleteJob: async (jobId: string) => {
        try {
          const jobResult = await service.getJob(jobId);

          if (!jobResult.success) {
            return jobResult as Result<void>;
          }

          const job = jobResult.data;
          const atomic = storage.atomic();

          // Delete job
          atomic.delete(Keys.job(jobId));

          // Remove from status index
          atomic.delete([...Keys.jobsByStatus(job.status), jobId]);

          // Delete job result if exists
          atomic.delete(Keys.jobResult(jobId));

          const result = await atomic.commit();

          if (!result.ok) {
            return Err(new Error("Failed to delete job: transaction failed"));
          }

          logger.debug("Job deleted", { jobId });
          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to delete job", error as Error, { jobId });
          return Err(error as Error);
        }
      },

      // Job results management
      saveJobResult: async (result: CrawlJobResult) => {
        try {
          await storage.set(Keys.jobResult(result.job.id), result);

          logger.debug("Job result saved", {
            jobId: result.job.id,
            regulationsCount: result.regulations.length,
            changesCount: result.changes.length
          });

          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to save job result", error as Error, { jobId: result.job.id });
          return Err(error as Error);
        }
      },

      getJobResult: async (jobId: string) => {
        try {
          const result = await storage.get<CrawlJobResult>(Keys.jobResult(jobId));

          if (!result.value) {
            return Err(new Error(`Job result not found: ${jobId}`));
          }

          return Ok(result.value);
        } catch (error) {
          logger.error("Failed to get job result", error as Error, { jobId });
          return Err(error as Error);
        }
      },

      // Regulation management
      saveRegulation: async (regulation: RegulationInput) => {
        try {
          const id = crypto.randomUUID();
          const now = new Date();

          const fullRegulation: Regulation = {
            id,
            ...regulation,
            version: 1,
            created_at: now,
            updated_at: now,
          };

          const atomic = storage.atomic();

          // Save regulation
          atomic.set(Keys.regulation(id), fullRegulation);

          // Index by country
          atomic.set([...Keys.regulationsByCountry(regulation.country_code), id], id);

          // Index by agency
          atomic.set([...Keys.regulationsByAgency(regulation.source_agency), id], id);

          // Index by category
          atomic.set([...Keys.regulationsByCategory(regulation.category), id], id);

          const result = await atomic.commit();

          if (!result.ok) {
            return Err(new Error("Failed to save regulation: transaction failed"));
          }

          logger.debug("Regulation saved", { id, country: regulation.country_code, agency: regulation.source_agency });
          return Ok(id);
        } catch (error) {
          logger.error("Failed to save regulation", error as Error);
          return Err(error as Error);
        }
      },

      getRegulation: async (id: string) => {
        try {
          const result = await storage.get<Regulation>(Keys.regulation(id));

          if (!result.value) {
            return Err(new Error(`Regulation not found: ${id}`));
          }

          return Ok(result.value);
        } catch (error) {
          logger.error("Failed to get regulation", error as Error, { id });
          return Err(error as Error);
        }
      },

      findRegulations: async (filters: RegulationFilters) => {
        try {
          const regulations: Regulation[] = [];
          let iter: AsyncIterable<{ key: readonly string[]; value: string }>;

          // Choose the most selective index
          if (filters.country) {
            iter = storage.list<string>({ prefix: Keys.regulationsByCountry(filters.country) });
          } else if (filters.agency) {
            iter = storage.list<string>({ prefix: Keys.regulationsByAgency(filters.agency) });
          } else if (filters.category) {
            iter = storage.list<string>({ prefix: Keys.regulationsByCategory(filters.category) });
          } else {
            iter = storage.list<string>({ prefix: ["regulations"] }, { limit: filters.limit || 100 });
          }

          for await (const entry of iter) {
            const regulationId = typeof entry.value === "string" ? entry.value : entry.key[1] as string;
            const regResult = await service.getRegulation(regulationId);

            if (regResult.success) {
              const regulation = regResult.data;

              // Apply additional filters
              if (filters.country && regulation.country_code !== filters.country) continue;
              if (filters.agency && regulation.source_agency !== filters.agency) continue;
              if (filters.category && regulation.category !== filters.category) continue;
              if (filters.fromDate && regulation.created_at < filters.fromDate) continue;
              if (filters.toDate && regulation.created_at > filters.toDate) continue;

              regulations.push(regulation);

              if (filters.limit && regulations.length >= filters.limit) break;
            }
          }

          return Ok(regulations);
        } catch (error) {
          logger.error("Failed to find regulations", error as Error, { filters });
          return Err(error as Error);
        }
      },

      updateRegulation: async (id: string, updates: Partial<RegulationInput>) => {
        try {
          const regResult = await service.getRegulation(id);

          if (!regResult.success) {
            return regResult as Result<void>;
          }

          const regulation = regResult.data;
          const updatedRegulation: Regulation = {
            ...regulation,
            ...updates,
            updated_at: new Date(),
          };

          await storage.set(Keys.regulation(id), updatedRegulation);

          logger.debug("Regulation updated", { id });
          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to update regulation", error as Error, { id });
          return Err(error as Error);
        }
      },

      deleteRegulation: async (id: string) => {
        try {
          const regResult = await service.getRegulation(id);

          if (!regResult.success) {
            return regResult as Result<void>;
          }

          const regulation = regResult.data;
          const atomic = storage.atomic();

          // Delete regulation
          atomic.delete(Keys.regulation(id));

          // Remove from indexes
          atomic.delete([...Keys.regulationsByCountry(regulation.country_code), id]);
          atomic.delete([...Keys.regulationsByAgency(regulation.source_agency), id]);
          atomic.delete([...Keys.regulationsByCategory(regulation.category), id]);

          const result = await atomic.commit();

          if (!result.ok) {
            return Err(new Error("Failed to delete regulation: transaction failed"));
          }

          logger.debug("Regulation deleted", { id });
          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to delete regulation", error as Error, { id });
          return Err(error as Error);
        }
      },

      // Change management
      saveChange: async (change: Change) => {
        try {
          const id = crypto.randomUUID();
          const fullChange: Change = {
            ...change,
            id,
          };

          const atomic = storage.atomic();

          // Save change
          atomic.set(Keys.change(id), fullChange);

          // Index by regulation
          if (change.regulation_id) {
            atomic.set([...Keys.changesByRegulation(change.regulation_id), id], id);
          }

          const result = await atomic.commit();

          if (!result.ok) {
            return Err(new Error("Failed to save change: transaction failed"));
          }

          logger.debug("Change saved", { id, regulationId: change.regulation_id });
          return Ok(id);
        } catch (error) {
          logger.error("Failed to save change", error as Error);
          return Err(error as Error);
        }
      },

      getChange: async (id: string) => {
        try {
          const result = await storage.get<Change>(Keys.change(id));

          if (!result.value) {
            return Err(new Error(`Change not found: ${id}`));
          }

          return Ok(result.value);
        } catch (error) {
          logger.error("Failed to get change", error as Error, { id });
          return Err(error as Error);
        }
      },

      getChangesByRegulation: async (regulationId: string) => {
        try {
          const changes: Change[] = [];
          const iter = storage.list<string>({ prefix: Keys.changesByRegulation(regulationId) });

          for await (const entry of iter) {
            const changeId = entry.value;
            const changeResult = await service.getChange(changeId);

            if (changeResult.success) {
              changes.push(changeResult.data);
            }
          }

          return Ok(changes);
        } catch (error) {
          logger.error("Failed to get changes by regulation", error as Error, { regulationId });
          return Err(error as Error);
        }
      },

      getRecentChanges: async (limit = 50) => {
        try {
          const changes: Change[] = [];
          const iter = storage.list<Change>({ prefix: ["changes"] }, { limit, reverse: true });

          for await (const entry of iter) {
            if (entry.value && typeof entry.value === "object" && "id" in entry.value) {
              changes.push(entry.value);
            }
          }

          return Ok(changes);
        } catch (error) {
          logger.error("Failed to get recent changes", error as Error, { limit });
          return Err(error as Error);
        }
      },

      // Web source management
      saveWebSource: async (source: WebSource) => {
        try {
          const atomic = storage.atomic();

          // Save web source
          atomic.set(Keys.webSource(source.id), source);

          // Index active sources
          if (source.active) {
            atomic.set([...Keys.activeWebSources(), source.id], source.id);
          }

          const result = await atomic.commit();

          if (!result.ok) {
            return Err(new Error("Failed to save web source: transaction failed"));
          }

          logger.debug("Web source saved", { id: source.id, name: source.name, active: source.active });
          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to save web source", error as Error, { id: source.id });
          return Err(error as Error);
        }
      },

      getWebSource: async (id: string) => {
        try {
          const result = await storage.get<WebSource>(Keys.webSource(id));

          if (!result.value) {
            return Err(new Error(`Web source not found: ${id}`));
          }

          return Ok(result.value);
        } catch (error) {
          logger.error("Failed to get web source", error as Error, { id });
          return Err(error as Error);
        }
      },

      getActiveWebSources: async () => {
        try {
          const sources: WebSource[] = [];
          const iter = storage.list<string>({ prefix: Keys.activeWebSources() });

          for await (const entry of iter) {
            const sourceId = entry.value;
            const sourceResult = await service.getWebSource(sourceId);

            if (sourceResult.success && sourceResult.data.active) {
              sources.push(sourceResult.data);
            }
          }

          return Ok(sources);
        } catch (error) {
          logger.error("Failed to get active web sources", error as Error);
          return Err(error as Error);
        }
      },

      updateWebSource: async (id: string, updates: Partial<WebSource>) => {
        try {
          const sourceResult = await service.getWebSource(id);

          if (!sourceResult.success) {
            return sourceResult as Result<void>;
          }

          const source = sourceResult.data;
          const updatedSource: WebSource = {
            ...source,
            ...updates,
          };

          const atomic = storage.atomic();

          // Update web source
          atomic.set(Keys.webSource(id), updatedSource);

          // Update active index
          if (source.active && !updatedSource.active) {
            // Remove from active index
            atomic.delete([...Keys.activeWebSources(), id]);
          } else if (!source.active && updatedSource.active) {
            // Add to active index
            atomic.set([...Keys.activeWebSources(), id], id);
          }

          const result = await atomic.commit();

          if (!result.ok) {
            return Err(new Error("Failed to update web source: transaction failed"));
          }

          logger.debug("Web source updated", { id });
          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to update web source", error as Error, { id });
          return Err(error as Error);
        }
      },

      deleteWebSource: async (id: string) => {
        try {
          const sourceResult = await service.getWebSource(id);

          if (!sourceResult.success) {
            return sourceResult as Result<void>;
          }

          const source = sourceResult.data;
          const atomic = storage.atomic();

          // Delete web source
          atomic.delete(Keys.webSource(id));

          // Remove from active index
          if (source.active) {
            atomic.delete([...Keys.activeWebSources(), id]);
          }

          const result = await atomic.commit();

          if (!result.ok) {
            return Err(new Error("Failed to delete web source: transaction failed"));
          }

          logger.debug("Web source deleted", { id });
          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to delete web source", error as Error, { id });
          return Err(error as Error);
        }
      },

      close: async () => {
        await storage.close();
        logger.info("Database service closed");
      },

      clear: async () => {
        return await storage.clear();
      },
    };

    return Ok(service);
  } catch (error) {
    logger.error("Failed to initialize database service", error as Error);
    return Err(error as Error);
  }
};
