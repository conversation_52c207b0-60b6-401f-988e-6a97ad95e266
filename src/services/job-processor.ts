// Job Processor Service - Functional Implementation
import { match } from "ts-pattern";
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";
import type { CrawlingService, CrawlJob } from "./crawling.ts";
import type { RegulationService } from "../server/dependencies.ts";

// Job processor configuration
export type JobProcessorConfig = {
  readonly checkIntervalMs: number;
  readonly maxRetries: number;
  readonly batchSize: number;
};

// Job processor service interface
export type JobProcessorService = {
  readonly start: () => void;
  readonly stop: () => void;
  readonly processJob: (jobId: string) => Promise<Result<void>>;
  readonly processAllJobs: () => Promise<Result<{ processed: number; failed: number }>>;
  readonly processAllCompletedJobs: () => Promise<Result<{ processed: number; failed: number }>>;
  readonly isRunning: () => boolean;
};

// Logger interface
type Logger = {
  readonly info: (message: string, meta?: Record<string, unknown>) => void;
  readonly error: (message: string, error?: Error, meta?: Record<string, unknown>) => void;
  readonly warn: (message: string, meta?: Record<string, unknown>) => void;
  readonly debug: (message: string, meta?: Record<string, unknown>) => void;
};

// Create job processor service
export const createJobProcessorService = (
  crawlingService: CrawlingService,
  regulationService: RegulationService,
  logger: Logger,
  config: JobProcessorConfig = {
    checkIntervalMs: 30000, // Check every 30 seconds
    maxRetries: 3,
    batchSize: 5,
  }
): JobProcessorService => {
  let intervalId: number | null = null;
  let isRunning = false;

  const processJob = async (jobId: string): Promise<Result<void>> => {
    try {
      logger.debug("Processing job", { jobId });

      // Get job status
      const jobResult = await crawlingService.getJobStatus(jobId);
      if (!jobResult.success) {
        return jobResult as Result<void>;
      }

      const job = jobResult.data;

      // Only process completed jobs
      if (job.status !== "completed") {
        logger.debug("Job not ready for processing", { jobId, status: job.status });
        return Ok(undefined);
      }

      // Process the completed job
      const processResult = await crawlingService.processCompletedJob(jobId);
      if (!processResult.success) {
        logger.error("Failed to process completed job", processResult.error, { jobId });
        return processResult as Result<void>;
      }

      const { regulations, changes } = processResult.data;

      // Save extracted regulations
      for (const regulation of regulations) {
        const saveResult = await regulationService.create(regulation);
        if (!saveResult.success) {
          logger.error("Failed to save regulation", saveResult.error, { jobId, regulation: regulation.title });
        }
      }

      logger.info("Job processed successfully", {
        jobId,
        regulationsExtracted: regulations.length,
        changesDetected: changes.length,
      });

      return Ok(undefined);
    } catch (error) {
      logger.error("Error processing job", error as Error, { jobId });
      return Err(error as Error);
    }
  };

  const processAllJobs = async (): Promise<Result<{ processed: number; failed: number }>> => {
    try {
      logger.debug("Processing all completed jobs");

      // Get completed jobs
      const completedJobsResult = await crawlingService.getJobsByStatus("completed");
      if (!completedJobsResult.success) {
        logger.warn("Could not retrieve completed jobs", { error: completedJobsResult.error.message });
        return Ok({ processed: 0, failed: 0 });
      }

      const completedJobs = completedJobsResult.data;
      let processed = 0;
      let failed = 0;

      // Process jobs in batches
      for (let i = 0; i < completedJobs.length; i += config.batchSize) {
        const batch = completedJobs.slice(i, i + config.batchSize);

        const batchPromises = batch.map(async (job) => {
          const result = await processJob(job.id);
          if (result.success) {
            processed++;
          } else {
            failed++;
          }
          return result;
        });

        await Promise.all(batchPromises);
      }

      logger.info("Batch job processing completed", { processed, failed });
      return Ok({ processed, failed });
    } catch (error) {
      logger.error("Error processing all jobs", error as Error);
      return Err(error as Error);
    }
  };

  const checkAndProcessJobs = async () => {
    if (!isRunning) return;

    try {
      await processAllJobs();
    } catch (error) {
      logger.error("Error in periodic job check", error as Error);
    }
  };

  const start = () => {
    if (isRunning) {
      logger.warn("Job processor already running");
      return;
    }

    isRunning = true;
    intervalId = setInterval(checkAndProcessJobs, config.checkIntervalMs);
    logger.info("Job processor started", {
      checkIntervalMs: config.checkIntervalMs,
      maxRetries: config.maxRetries,
      batchSize: config.batchSize
    });
  };

  const stop = () => {
    if (!isRunning) {
      logger.warn("Job processor not running");
      return;
    }

    isRunning = false;
    if (intervalId !== null) {
      clearInterval(intervalId);
      intervalId = null;
    }
    logger.info("Job processor stopped");
  };

  return {
    start,
    stop,
    processJob,
    processAllJobs,
    processAllCompletedJobs: processAllJobs, // Alias for backward compatibility
    isRunning: () => isRunning,
  };
};

// Default configuration
export const defaultJobProcessorConfig: JobProcessorConfig = {
  checkIntervalMs: 30000, // 30 seconds
  maxRetries: 3,
  batchSize: 5,
};
