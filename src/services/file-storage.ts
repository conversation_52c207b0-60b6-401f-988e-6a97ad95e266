// File System Storage Service - Fallback for when Deno KV is not available
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";

// File storage configuration
export type FileStorageConfig = {
  readonly storageDir: string;
  readonly enableIndexes?: boolean;
};

// File storage interface - matches KV-like operations
export type FileStorage = {
  readonly get: <T>(key: readonly string[]) => Promise<Result<T | null>>;
  readonly set: (key: readonly string[], value: unknown) => Promise<Result<void>>;
  readonly delete: (key: readonly string[]) => Promise<Result<void>>;
  readonly list: <T>(prefix: { prefix: readonly string[] }, options?: { limit?: number }) => AsyncIterable<{ key: readonly string[]; value: T }>;
  readonly atomic: () => FileStorageAtomic;
  readonly close: () => Promise<void>;
  readonly clear: () => Promise<Result<void>>;
};

// Atomic operations for file storage
export type FileStorageAtomic = {
  readonly set: (key: readonly string[], value: unknown) => FileStorageAtomic;
  readonly delete: (key: readonly string[]) => FileStorageAtomic;
  readonly commit: () => Promise<{ ok: boolean; error?: string }>;
};

// Logger interface
type Logger = {
  readonly info: (message: string, meta?: Record<string, unknown>) => void;
  readonly error: (message: string, error?: Error, meta?: Record<string, unknown>) => void;
  readonly warn: (message: string, meta?: Record<string, unknown>) => void;
  readonly debug: (message: string, meta?: Record<string, unknown>) => void;
};

// Create file storage service
export const createFileStorage = async (
  config: FileStorageConfig,
  logger: Logger
): Promise<Result<FileStorage>> => {
  try {
    // Ensure storage directory exists
    await Deno.mkdir(config.storageDir, { recursive: true });
    
    // Create indexes directory if enabled
    if (config.enableIndexes) {
      await Deno.mkdir(`${config.storageDir}/indexes`, { recursive: true });
    }

    logger.info("File storage initialized", {
      storageDir: config.storageDir,
      enableIndexes: config.enableIndexes
    });

    // Helper functions
    const keyToPath = (key: readonly string[]): string => {
      const safeKey = key.map(k => encodeURIComponent(k)).join('__');
      return `${config.storageDir}/${safeKey}.json`;
    };

    const pathToKey = (path: string): readonly string[] => {
      const filename = path.split('/').pop()?.replace('.json', '') || '';
      return filename.split('__').map(k => decodeURIComponent(k));
    };

    const writeFile = async (path: string, data: unknown): Promise<Result<void>> => {
      try {
        const dir = path.substring(0, path.lastIndexOf('/'));
        await Deno.mkdir(dir, { recursive: true });
        await Deno.writeTextFile(path, JSON.stringify(data, null, 2));
        return Ok(undefined);
      } catch (error) {
        return Err(error as Error);
      }
    };

    const readFile = async <T>(path: string): Promise<Result<T | null>> => {
      try {
        const data = await Deno.readTextFile(path);
        return Ok(JSON.parse(data) as T);
      } catch (error) {
        if (error instanceof Deno.errors.NotFound) {
          return Ok(null);
        }
        return Err(error as Error);
      }
    };

    const deleteFile = async (path: string): Promise<Result<void>> => {
      try {
        await Deno.remove(path);
        return Ok(undefined);
      } catch (error) {
        if (error instanceof Deno.errors.NotFound) {
          return Ok(undefined); // Already deleted
        }
        return Err(error as Error);
      }
    };

    const listFiles = async function* <T>(
      prefix: { prefix: readonly string[] },
      options?: { limit?: number }
    ): AsyncIterable<{ key: readonly string[]; value: T }> {
      try {
        const prefixPath = prefix.prefix.map(k => encodeURIComponent(k)).join('__');
        let count = 0;
        
        for await (const entry of Deno.readDir(config.storageDir)) {
          if (options?.limit && count >= options.limit) {
            break;
          }

          if (entry.isFile && entry.name.endsWith('.json')) {
            const filename = entry.name.replace('.json', '');
            
            if (filename.startsWith(prefixPath)) {
              const fullPath = `${config.storageDir}/${entry.name}`;
              const readResult = await readFile<T>(fullPath);
              
              if (readResult.success && readResult.data !== null) {
                yield {
                  key: pathToKey(fullPath),
                  value: readResult.data
                };
                count++;
              }
            }
          }
        }
      } catch (error) {
        logger.error("Error listing files", error as Error, { prefix: prefix.prefix });
      }
    };

    // Atomic operations implementation
    const createAtomic = (): FileStorageAtomic => {
      const operations: Array<{
        type: 'set' | 'delete';
        key: readonly string[];
        value?: unknown;
      }> = [];

      return {
        set: (key: readonly string[], value: unknown) => {
          operations.push({ type: 'set', key, value });
          return createAtomic(); // Return new instance for chaining
        },

        delete: (key: readonly string[]) => {
          operations.push({ type: 'delete', key });
          return createAtomic(); // Return new instance for chaining
        },

        commit: async () => {
          try {
            // Execute all operations
            for (const op of operations) {
              const path = keyToPath(op.key);
              
              if (op.type === 'set') {
                const result = await writeFile(path, op.value);
                if (!result.success) {
                  return { ok: false, error: result.error.message };
                }
              } else if (op.type === 'delete') {
                const result = await deleteFile(path);
                if (!result.success) {
                  return { ok: false, error: result.error.message };
                }
              }
            }
            
            return { ok: true };
          } catch (error) {
            return { ok: false, error: (error as Error).message };
          }
        }
      };
    };

    const storage: FileStorage = {
      get: async <T>(key: readonly string[]) => {
        const path = keyToPath(key);
        return await readFile<T>(path);
      },

      set: async (key: readonly string[], value: unknown) => {
        const path = keyToPath(key);
        return await writeFile(path, value);
      },

      delete: async (key: readonly string[]) => {
        const path = keyToPath(key);
        return await deleteFile(path);
      },

      list: listFiles,

      atomic: createAtomic,

      close: async () => {
        logger.info("File storage closed");
      },

      clear: async () => {
        try {
          // Remove all files in storage directory
          for await (const entry of Deno.readDir(config.storageDir)) {
            if (entry.isFile && entry.name.endsWith('.json')) {
              await Deno.remove(`${config.storageDir}/${entry.name}`);
            }
          }
          logger.info("File storage cleared");
          return Ok(undefined);
        } catch (error) {
          logger.error("Failed to clear file storage", error as Error);
          return Err(error as Error);
        }
      }
    };

    return Ok(storage);
  } catch (error) {
    logger.error("Failed to initialize file storage", error as Error);
    return Err(error as Error);
  }
};

// Default configuration
export const defaultFileStorageConfig: FileStorageConfig = {
  storageDir: "./data/storage",
  enableIndexes: true,
};