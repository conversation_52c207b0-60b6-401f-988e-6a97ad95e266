// Functional router implementation with pattern matching
import { match } from "ts-pattern";
import { contentType } from "@std/media-types";
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";

// Route handler types
export type RouteHandler = (req: Request) => Promise<Response | null>;
export type RouteParams = Record<string, string>;

// Route matcher configuration
export type RouteConfig = {
  readonly method: string;
  readonly pattern: RegExp;
  readonly handler: (req: Request, params: RouteParams) => Promise<Response>;
};

// CORS configuration
export type CorsConfig = {
  readonly origin: string | string[];
  readonly methods: string[];
  readonly headers: string[];
  readonly credentials?: boolean;
};

// Router configuration
export type RouterConfig = {
  readonly cors?: CorsConfig;
  readonly staticDir?: string;
  readonly notFoundHandler?: RouteHandler;
  readonly errorHandler?: (error: Error) => Response;
};

// Create a functional router
export const createRouter = (
  routes: readonly RouteConfig[],
  config: RouterConfig = {}
) => {
  const defaultCors: CorsConfig = {
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    headers: ["Content-Type", "Authorization"],
  };

  const corsConfig = { ...defaultCors, ...config.cors };

  return async (req: Request): Promise<Response> => {
    const url = new URL(req.url);
    const method = req.method;

    // Handle CORS preflight
    if (method === "OPTIONS") {
      return createCorsResponse(corsConfig);
    }

    try {
      // Try static file serving first
      if (config.staticDir && url.pathname.startsWith("/static/")) {
        const staticResponse = await serveStatic(url.pathname, config.staticDir);
        if (staticResponse) {
          return addCorsHeaders(staticResponse, corsConfig);
        }
      }

      // Try to match routes
      for (const route of routes) {
        if (route.method === method) {
          const match = url.pathname.match(route.pattern);
          if (match) {
            const params = extractParams(match);
            const response = await route.handler(req, params);
            return addCorsHeaders(response, corsConfig);
          }
        }
      }

      // Handle not found
      if (config.notFoundHandler) {
        const response = await config.notFoundHandler(req);
        return response ? addCorsHeaders(response, corsConfig) : createNotFoundResponse();
      }

      return createNotFoundResponse();
    } catch (error) {
      const errorResponse = config.errorHandler
        ? config.errorHandler(error as Error)
        : createErrorResponse(error as Error);
      return addCorsHeaders(errorResponse, corsConfig);
    }
  };
};

// Extract route parameters from regex match
const extractParams = (match: RegExpMatchArray): RouteParams => {
  const params: RouteParams = {};
  
  // Skip the full match (index 0) and process capture groups
  for (let i = 1; i < match.length; i++) {
    if (match[i] !== undefined) {
      params[`param${i}`] = match[i]!;
    }
  }
  
  return params;
};

// Serve static files
const serveStatic = async (
  pathname: string,
  staticDir: string
): Promise<Response | null> => {
  try {
    const filePath = pathname.replace("/static/", "");
    const fullPath = `${staticDir}/${filePath}`;
    
    const file = await Deno.readFile(fullPath);
    
    // Get MIME type with explicit CSS handling
    let mimeType = contentType(filePath);
    if (!mimeType) {
      if (filePath.endsWith('.css')) {
        mimeType = 'text/css';
      } else if (filePath.endsWith('.js')) {
        mimeType = 'application/javascript';
      } else if (filePath.endsWith('.html')) {
        mimeType = 'text/html';
      } else {
        mimeType = 'application/octet-stream';
      }
    }
    
    return new Response(file, {
      headers: {
        "Content-Type": mimeType,
        "Cache-Control": "public, max-age=3600",
      },
    });
  } catch {
    return null;
  }
};

// CORS helpers
const createCorsResponse = (config: CorsConfig): Response => {
  return new Response(null, {
    status: 200,
    headers: getCorsHeaders(config),
  });
};

const getCorsHeaders = (config: CorsConfig): Record<string, string> => {
  const origin = Array.isArray(config.origin) 
    ? config.origin.join(", ") 
    : config.origin;

  return {
    "Access-Control-Allow-Origin": origin,
    "Access-Control-Allow-Methods": config.methods.join(", "),
    "Access-Control-Allow-Headers": config.headers.join(", "),
    ...(config.credentials && { "Access-Control-Allow-Credentials": "true" }),
  };
};

const addCorsHeaders = (response: Response, config: CorsConfig): Response => {
  const corsHeaders = getCorsHeaders(config);
  const newResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers,
  });

  Object.entries(corsHeaders).forEach(([key, value]) => {
    newResponse.headers.set(key, value);
  });

  return newResponse;
};

// Response creators
const createNotFoundResponse = (): Response => {
  return new Response("Not Found", { status: 404 });
};

const createErrorResponse = (error: Error): Response => {
  return new Response(
    JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
    }),
    {
      status: 500,
      headers: { "Content-Type": "application/json" },
    }
  );
};

// Request/Response helpers
export const parseJsonBody = async <T>(req: Request): Promise<Result<T>> => {
  try {
    const body = await req.json();
    return Ok(body as T);
  } catch (error) {
    return Err(error instanceof Error ? error : new Error(String(error)));
  }
};

export const createJsonResponse = <T>(
  data: T,
  status = 200
): Response => {
  return new Response(JSON.stringify({
    success: true,
    data,
    timestamp: new Date().toISOString(),
  }), {
    status,
    headers: { "Content-Type": "application/json" },
  });
};

export const createErrorJsonResponse = (
  error: string | Error,
  status = 500
): Response => {
  const message = error instanceof Error ? error.message : error;
  
  return new Response(JSON.stringify({
    success: false,
    error: message,
    timestamp: new Date().toISOString(),
  }), {
    status,
    headers: { "Content-Type": "application/json" },
  });
};

// Route builder helpers
export const route = (
  method: string,
  pattern: string | RegExp,
  handler: (req: Request, params: RouteParams) => Promise<Response>
): RouteConfig => ({
  method,
  pattern: typeof pattern === "string" ? new RegExp(`^${pattern}$`) : pattern,
  handler,
});

export const get = (
  pattern: string | RegExp,
  handler: (req: Request, params: RouteParams) => Promise<Response>
): RouteConfig => route("GET", pattern, handler);

export const post = (
  pattern: string | RegExp,
  handler: (req: Request, params: RouteParams) => Promise<Response>
): RouteConfig => route("POST", pattern, handler);

export const put = (
  pattern: string | RegExp,
  handler: (req: Request, params: RouteParams) => Promise<Response>
): RouteConfig => route("PUT", pattern, handler);

export const del = (
  pattern: string | RegExp,
  handler: (req: Request, params: RouteParams) => Promise<Response>
): RouteConfig => route("DELETE", pattern, handler);

// Pattern matching route dispatcher
export const matchRoute = (req: Request) => {
  const url = new URL(req.url);
  const method = req.method;
  const pathname = url.pathname;
  
  return match({ method, pathname })
    .with({ method: "GET", pathname: "/" }, () => "home")
    .with({ method: "GET", pathname: "/health" }, () => "health")
    .with({ method: "POST", pathname: "/api/webhook" }, () => "webhook")
    .otherwise(() => "not_found");
};