// Server-side templates for HTMX responses

import type { Regulation } from "../types/regulation.ts";
import type { Change } from "../types/change.ts";

// Dashboard stats template
export const renderStatsTemplate = (data: any): string => {
  const { overview } = data;
  
  return `
    <div class="stat-card">
      <div class="stat-icon">📊</div>
      <div class="stat-content">
        <div class="stat-number">${overview.totalRegulations}</div>
        <div class="stat-label">Total Regulations</div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">🔄</div>
      <div class="stat-content">
        <div class="stat-number">${overview.totalChanges}</div>
        <div class="stat-label">Total Changes</div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">⚡</div>
      <div class="stat-content">
        <div class="stat-number">${overview.recentChanges}</div>
        <div class="stat-label">Recent Changes</div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">🌍</div>
      <div class="stat-content">
        <div class="stat-number">${overview.countriesMonitored}</div>
        <div class="stat-label">Countries Monitored</div>
      </div>
    </div>
  `;
};

// Recent changes template
export const renderChangesTemplate = (changes: readonly Change[]): string => {
  if (changes.length === 0) {
    return `
      <div class="activity-item">
        <div class="activity-icon">📋</div>
        <div class="activity-content">
          <div class="activity-title">No recent changes</div>
          <div class="activity-meta">All regulations are up to date</div>
        </div>
      </div>
    `;
  }
  
  const priorityIcons: Record<string, string> = {
    critical: '🚨',
    high: '⚠️',
    medium: '📝',
    low: '📋'
  };
  
  return changes.map(change => `
    <div class="activity-item">
      <div class="activity-icon">${priorityIcons[change.priority] || '📝'}</div>
      <div class="activity-content">
        <div class="activity-title">${change.summary['en'] || 'Change detected'}</div>
        <div class="activity-meta">
          ${change.change_type.replace('_', ' ')} • 
          ${new Date(change.detected_at).toLocaleDateString()}
        </div>
      </div>
      <div class="activity-time">${new Date(change.detected_at).toLocaleTimeString()}</div>
    </div>
  `).join('');
};

// Regulations list template
export const renderRegulationsTemplate = (regulations: readonly Regulation[]): string => {
  if (regulations.length === 0) {
    return `
      <div class="empty-state">
        <div class="empty-icon">📋</div>
        <div>No regulations found</div>
        <div style="margin-top: 0.5rem; font-size: 0.875rem;">Try adjusting your filters.</div>
      </div>
    `;
  }
  
  return regulations.map(reg => `
    <div class="regulation-card">
      <div class="regulation-header">
        <div class="regulation-title">${reg.title['en'] || 'Untitled Regulation'}</div>
        <div class="regulation-meta">
          <span class="regulation-badge badge-country">${reg.country_code}</span>
          <span class="regulation-badge badge-category">${reg.category.replace('_', ' ')}</span>
        </div>
      </div>
      <div class="regulation-body">
        <div class="regulation-description">
          ${reg.description['en'] || 'No description available'}
        </div>
        <div class="regulation-details">
          <div class="detail-item">
            <div class="detail-label">Effective Date</div>
            <div class="detail-value">${new Date(reg.timeline.effective_date).toLocaleDateString()}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">Source Agency</div>
            <div class="detail-value">${reg.source_agency}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">HS Codes</div>
            <div class="detail-value">${reg.hs_codes.join(', ') || 'None specified'}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">Economic Impact</div>
            <div class="detail-value">
              <div class="impact-bar">
                <div class="impact-progress">
                  <div class="impact-fill" style="width: ${reg.impact_assessment.economic * 10}%"></div>
                </div>
                <span>${reg.impact_assessment.economic}/10</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `).join('');
};

// Changes list template
export const renderChangesListTemplate = (changes: readonly Change[]): string => {
  if (changes.length === 0) {
    return `
      <div class="empty-state">
        <div class="empty-icon">🔄</div>
        <div>No changes found</div>
        <div style="margin-top: 0.5rem; font-size: 0.875rem;">Try adjusting your filters.</div>
      </div>
    `;
  }
  
  const priorityColors: Record<string, string> = {
    critical: '#dc2626',
    high: '#d97706',
    medium: '#0891b2',
    low: '#64748b'
  };
  
  return changes.map(change => `
    <div class="change-item" data-priority="${change.priority}">
      <div class="change-header">
        <div class="change-title">${change.summary['en'] || 'Change Detected'}</div>
        <span class="priority-badge priority-${change.priority}">${change.priority}</span>
      </div>
      <div class="change-body">
        <div class="change-summary">
          ${change.detailed_changes.map((detail: any) => detail.change_description).join('; ')}
        </div>
        <div class="change-meta">
          <span class="change-type">${change.change_type.replace('_', ' ')}</span>
          <span>•</span>
          <span>Detected: ${new Date(change.detected_at).toLocaleDateString()}</span>
          <span>•</span>
          <span>Impact: ${Math.round((change.impact_score.economic + change.impact_score.operational + change.impact_score.compliance + change.impact_score.urgency) / 4)}/10</span>
        </div>
        ${change.affected_industries.length > 0 ? `
          <div class="change-industries" style="margin-top: 1rem;">
            ${change.affected_industries.map((industry: any) => `
              <span class="industry-tag">${industry}</span>
            `).join('')}
          </div>
        ` : ''}
      </div>
    </div>
  `).join('');
};