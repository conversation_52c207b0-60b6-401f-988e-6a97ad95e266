// RESTful API Server for WTO Compliance System

import { join } from "@std/path";
import type { Priority } from "../types/core.ts";
import type { Regulation } from "../types/regulation.ts";
import type { Change, ChangeSubscription } from "../types/change.ts";
import { DataCollectionService, createWTODataSources } from "../services/data-collection.ts";
import { DataProcessingService } from "../services/data-processing.ts";
import { ChangeDetectionService } from "../services/change-detection.ts";
import { NotificationService } from "../services/notification.ts";
import { 
  renderStatsTemplate, 
  renderChangesTemplate, 
  renderRegulationsTemplate, 
  renderChangesListTemplate 
} from "./templates.ts";

// API Response types
type ApiResponse<T> = {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly timestamp: string;
};

// API endpoints configuration
type ApiEndpoint = {
  readonly method: string;
  readonly path: RegExp;
  readonly handler: (request: Request, params: string[]) => Promise<Response>;
};

// Mock data for demonstration
const mockRegulations: Regulation[] = [
  {
    id: "reg-1",
    country_code: "US",
    source_agency: "US Trade.gov",
    title: { en: "New Semiconductor Export Controls" },
    description: { en: "Enhanced export controls on advanced semiconductor technology to protect national security." },
    category: "technical_regulation",
    subcategory: "export_control",
    hs_codes: ["8542", "8541"],
    timeline: {
      effective_date: new Date("2024-01-15"),
      implementation_deadline: new Date("2024-03-15"),
    },
    impact_assessment: {
      economic: 9,
      operational: 8,
      compliance: 9,
      urgency: 8,
    },
    related_regulations: [],
    original_language: "en",
    document_metadata: {
      source_url: "https://trade.gov/semiconductor-controls",
      document_hash: "abc123",
      content_type: "text/html",
      file_size: 15420,
      extracted_at: new Date("2024-01-10"),
    },
    version: 1,
    created_at: new Date("2024-01-10"),
    updated_at: new Date("2024-01-10"),
  },
  {
    id: "reg-2",
    country_code: "EU",
    source_agency: "European Commission",
    title: { 
      en: "Digital Services Act Implementation",
      fr: "Mise en œuvre de la loi sur les services numériques"
    },
    description: { 
      en: "New regulations for digital platforms operating in the European Union.",
      fr: "Nouvelles réglementations pour les plateformes numériques opérant dans l'Union européenne."
    },
    category: "services_regulation",
    subcategory: "digital_services",
    hs_codes: ["8471", "8473"],
    timeline: {
      effective_date: new Date("2024-02-01"),
      implementation_deadline: new Date("2024-08-01"),
    },
    impact_assessment: {
      economic: 7,
      operational: 9,
      compliance: 8,
      urgency: 6,
    },
    related_regulations: [],
    original_language: "en",
    document_metadata: {
      source_url: "https://ec.europa.eu/digital-services-act",
      document_hash: "def456",
      content_type: "text/html",
      file_size: 23100,
      extracted_at: new Date("2024-01-05"),
    },
    version: 1,
    created_at: new Date("2024-01-05"),
    updated_at: new Date("2024-01-05"),
  },
  {
    id: "reg-3",
    country_code: "JP",
    source_agency: "JETRO",
    title: { en: "Updated Food Safety Standards" },
    description: { en: "Revised food safety standards for imported agricultural products." },
    category: "sanitary_phytosanitary",
    subcategory: "food_safety",
    hs_codes: ["0701", "0702", "0703"],
    timeline: {
      effective_date: new Date("2024-03-01"),
    },
    impact_assessment: {
      economic: 6,
      operational: 7,
      compliance: 8,
      urgency: 5,
    },
    related_regulations: [],
    original_language: "en",
    document_metadata: {
      source_url: "https://jetro.go.jp/food-safety",
      document_hash: "ghi789",
      content_type: "text/html",
      file_size: 8900,
      extracted_at: new Date("2024-01-08"),
    },
    version: 1,
    created_at: new Date("2024-01-08"),
    updated_at: new Date("2024-01-08"),
  }
];

const mockChanges: Change[] = [
  {
    id: "change-1",
    regulation_id: "reg-1",
    change_type: "amendment",
    priority: "critical",
    impact_score: {
      economic: 9,
      operational: 8,
      compliance: 9,
      urgency: 8,
    },
    summary: { en: "Extended semiconductor export controls to additional product categories" },
    detailed_changes: [
      {
        field: "hs_codes",
        previous_value: ["8542"],
        new_value: ["8542", "8541"],
        change_description: "Added HS code 8541 to covered products",
        confidence_score: 0.95,
      }
    ],
    affected_industries: ["Technology", "Electronics", "Manufacturing"],
    implementation_date: new Date("2024-03-15"),
    stakeholders: ["importers", "exporters", "manufacturers"],
    detected_at: new Date("2024-01-12"),
    verified_at: new Date("2024-01-12"),
    notification_sent: true,
  },
  {
    id: "change-2",
    regulation_id: "reg-2",
    change_type: "effective_date_change",
    priority: "medium",
    impact_score: {
      economic: 6,
      operational: 8,
      compliance: 7,
      urgency: 5,
    },
    summary: { en: "Implementation deadline extended by 2 months" },
    detailed_changes: [
      {
        field: "timeline.implementation_deadline",
        previous_value: new Date("2024-06-01"),
        new_value: new Date("2024-08-01"),
        change_description: "Implementation deadline extended",
        confidence_score: 1.0,
      }
    ],
    affected_industries: ["Technology", "Digital Services"],
    implementation_date: new Date("2024-08-01"),
    stakeholders: ["digital_platforms", "regulatory_bodies"],
    detected_at: new Date("2024-01-15"),
    notification_sent: false,
  }
];

// API Server implementation
export class ApiServer {
  private readonly _dataCollection: DataCollectionService;
  private readonly _dataProcessing: DataProcessingService;
  private readonly _changeDetection: ChangeDetectionService;
  private readonly _notification: NotificationService;
  private readonly endpoints: ApiEndpoint[];

  constructor() {
    const dataSources = createWTODataSources();
    this._dataCollection = new DataCollectionService(dataSources);
    this._dataProcessing = new DataProcessingService();
    this._changeDetection = new ChangeDetectionService();
    this._notification = new NotificationService([]);

    this.endpoints = [
      {
        method: "GET",
        path: /^\/api\/regulations$/,
        handler: this.getRegulations.bind(this),
      },
      {
        method: "GET",
        path: /^\/api\/regulations\/([^\/]+)$/,
        handler: this.getRegulation.bind(this),
      },
      {
        method: "GET",
        path: /^\/api\/changes$/,
        handler: this.getChanges.bind(this),
      },
      {
        method: "GET",
        path: /^\/api\/countries\/([^\/]+)\/regulations$/,
        handler: this.getRegulationsByCountry.bind(this),
      },
      {
        method: "GET",
        path: /^\/api\/dashboard\/stats$/,
        handler: this.getDashboardStats.bind(this),
      },
      {
        method: "POST",
        path: /^\/api\/subscriptions$/,
        handler: this.createSubscription.bind(this),
      },
      {
        method: "GET",
        path: /^\/$/,
        handler: this.serveIndex.bind(this),
      },
      {
        method: "GET",
        path: /^\/static\/(.+)$/,
        handler: this.serveStatic.bind(this),
      },
    ];
  }

  async start(port = 8000): Promise<void> {
    console.log(`🚀 WTO Compliance API Server starting on port ${port}`);
    console.log(`📱 Dashboard available at: http://localhost:${port}`);
    
    const server = Deno.serve({ port }, this.handleRequest.bind(this));
    await server.finished;
  }

  private async handleRequest(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const method = request.method;

    // Enable CORS
    const corsHeaders = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    };

    if (method === "OPTIONS") {
      return new Response(null, { status: 200, headers: corsHeaders });
    }

    // Find matching endpoint
    for (const endpoint of this.endpoints) {
      if (endpoint.method === method) {
        const match = url.pathname.match(endpoint.path);
        if (match) {
          try {
            const response = await endpoint.handler(request, match.slice(1));
            // Add CORS headers to response
            for (const [key, value] of Object.entries(corsHeaders)) {
              response.headers.set(key, value);
            }
            return response;
          } catch (error) {
            return this.errorResponse(
              error instanceof Error ? error.message : "Internal server error",
              500,
              corsHeaders
            );
          }
        }
      }
    }

    return this.errorResponse("Not found", 404, corsHeaders);
  }

  // API Handlers
  private getRegulations(request: Request, _params: string[]): Promise<Response> {
    const url = new URL(request.url);
    const country = url.searchParams.get("country");
    const category = url.searchParams.get("category");
    const _priority = url.searchParams.get("priority") as Priority;
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const offset = parseInt(url.searchParams.get("offset") || "0");

    let filteredRegulations = [...mockRegulations];

    if (country) {
      filteredRegulations = filteredRegulations.filter(r => r.country_code === country);
    }

    if (category) {
      filteredRegulations = filteredRegulations.filter(r => r.category === category);
    }

    const totalCount = filteredRegulations.length;
    const paginatedResults = filteredRegulations.slice(offset, offset + limit);

    // Check if this is an HTMX request
    const isHTMX = request.headers.get("HX-Request") === "true";
    
    if (isHTMX) {
      return Promise.resolve(new Response(renderRegulationsTemplate(paginatedResults), {
        headers: { "Content-Type": "text/html" },
      }));
    }

    return Promise.resolve(this.successResponse({
      regulations: paginatedResults,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount,
      },
    }));
  }

  private getRegulation(_request: Request, params: string[]): Promise<Response> {
    const id = params[0];
    const regulation = mockRegulations.find(r => r.id === id);

    if (!regulation) {
      return Promise.resolve(this.errorResponse("Regulation not found", 404));
    }

    const changes = mockChanges.filter(c => c.regulation_id === id);

    return Promise.resolve(this.successResponse({
      regulation,
      changes,
    }));
  }

  private getChanges(request: Request, _params: string[]): Promise<Response> {
    const url = new URL(request.url);
    const priority = url.searchParams.get("priority") as Priority;
    const since = url.searchParams.get("since");
    const limit = parseInt(url.searchParams.get("limit") || "20");

    let filteredChanges = [...mockChanges];

    if (priority) {
      filteredChanges = filteredChanges.filter(c => c.priority === priority);
    }

    if (since) {
      const sinceDate = new Date(since);
      filteredChanges = filteredChanges.filter(c => c.detected_at > sinceDate);
    }

    const paginatedChanges = filteredChanges.slice(0, limit);

    // Check if this is an HTMX request
    const isHTMX = request.headers.get("HX-Request") === "true";
    
    if (isHTMX) {
      // For activity feed (limit 5) use different template
      if (limit <= 5) {
        return Promise.resolve(new Response(renderChangesTemplate(paginatedChanges), {
          headers: { "Content-Type": "text/html" },
        }));
      } else {
        return Promise.resolve(new Response(renderChangesListTemplate(paginatedChanges), {
          headers: { "Content-Type": "text/html" },
        }));
      }
    }

    return Promise.resolve(this.successResponse({
      changes: paginatedChanges,
      total: filteredChanges.length,
    }));
  }

  private getRegulationsByCountry(_request: Request, params: string[]): Promise<Response> {
    const countryCode = params[0].toUpperCase();
    const regulations = mockRegulations.filter(r => r.country_code === countryCode);

    return Promise.resolve(this.successResponse({
      country: countryCode,
      regulations,
      count: regulations.length,
    }));
  }

  private getDashboardStats(request: Request, _params: string[]): Promise<Response> {
    const totalRegulations = mockRegulations.length;
    const totalChanges = mockChanges.length;
    const recentChanges = mockChanges.filter(c => 
      c.detected_at > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    );

    const countryCounts = mockRegulations.reduce((acc, reg) => {
      acc[reg.country_code] = (acc[reg.country_code] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const categoryCounts = mockRegulations.reduce((acc, reg) => {
      acc[reg.category] = (acc[reg.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const priorityCounts = mockChanges.reduce((acc, change) => {
      acc[change.priority] = (acc[change.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const data = {
      overview: {
        totalRegulations,
        totalChanges,
        recentChanges: recentChanges.length,
        countriesMonitored: Object.keys(countryCounts).length,
      },
      distributions: {
        byCountry: countryCounts,
        byCategory: categoryCounts,
        changesByPriority: priorityCounts,
      },
      recentActivity: recentChanges.slice(0, 5),
    };

    // Check if this is an HTMX request
    const isHTMX = request.headers.get("HX-Request") === "true";
    
    if (isHTMX) {
      return Promise.resolve(new Response(renderStatsTemplate(data), {
        headers: { "Content-Type": "text/html" },
      }));
    }

    return Promise.resolve(this.successResponse(data));
  }

  private async createSubscription(request: Request, _params: string[]): Promise<Response> {
    try {
      const body = await request.json();
      
      // Validate subscription data
      const subscription: ChangeSubscription = {
        id: crypto.randomUUID(),
        user_id: body.user_id || "demo-user",
        name: body.name || "New Subscription",
        country_codes: body.country_codes || [],
        regulation_categories: body.regulation_categories || [],
        hs_codes: body.hs_codes || [],
        priority_threshold: body.priority_threshold || "medium",
        notification_config: {
          enabled: true,
          priority_threshold: body.priority_threshold || "medium",
          channels: body.channels || [],
          delay_minutes: 0,
          batch_notifications: false,
          custom_filters: [],
        },
        created_at: new Date(),
        updated_at: new Date(),
        active: true,
      };

      return this.successResponse(subscription, 201);
    } catch (_error) {
      return this.errorResponse("Invalid subscription data", 400);
    }
  }

  // Static file serving
  private async serveIndex(_request: Request, _params: string[]): Promise<Response> {
    try {
      const indexPath = join(Deno.cwd(), "src", "ui", "index.html");
      const content = await Deno.readTextFile(indexPath);
      
      return new Response(content, {
        headers: {
          "Content-Type": "text/html",
          "Cache-Control": "no-cache",
        },
      });
    } catch {
      return this.errorResponse("Index page not found", 404);
    }
  }

  private async serveStatic(_request: Request, params: string[]): Promise<Response> {
    const filePath = params[0];
    
    try {
      const fullPath = join(Deno.cwd(), "src", "ui", filePath);
      const content = await Deno.readTextFile(fullPath);
      
      const contentType = this.getContentType(filePath);
      
      return new Response(content, {
        headers: {
          "Content-Type": contentType,
          "Cache-Control": "public, max-age=3600",
        },
      });
    } catch {
      return this.errorResponse("File not found", 404);
    }
  }

  // Helper methods
  private successResponse<T>(data: T, status = 200): Response {
    const response: ApiResponse<T> = {
      success: true,
      data,
      timestamp: new Date().toISOString(),
    };

    return new Response(JSON.stringify(response), {
      status,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  private errorResponse(error: string, status = 500, additionalHeaders: Record<string, string> = {}): Response {
    const response: ApiResponse<never> = {
      success: false,
      error,
      timestamp: new Date().toISOString(),
    };

    const headers = {
      "Content-Type": "application/json",
      ...additionalHeaders,
    };

    return new Response(JSON.stringify(response), {
      status,
      headers,
    });
  }

  private getContentType(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase();
    
    const mimeTypes: Record<string, string> = {
      'html': 'text/html',
      'css': 'text/css',
      'js': 'text/javascript',
      'json': 'application/json',
      'png': 'image/png',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'gif': 'image/gif',
      'svg': 'image/svg+xml',
    };

    return mimeTypes[ext || ''] || 'text/plain';
  }
}

// Factory function
export const createApiServer = (): ApiServer => {
  return new ApiServer();
};