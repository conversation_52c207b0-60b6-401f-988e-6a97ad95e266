// API routes for crawling and web source management
import { match } from "ts-pattern";
import { get, post, put, del, createJsonResponse, createErrorJsonResponse, parseJsonBody } from "../lib/router.ts";
import type { Services } from "./dependencies.ts";
import type { RouteConfig, RouteParams } from "../lib/router.ts";
import { isOk, isErr, type Result } from "../types/core.ts";
import type { WebSource } from "../services/firecrawl.ts";
import type { CrawlJob } from "../services/crawling.ts";
import { getWebSources, getWebSourceById } from "../data/web-sources.ts";

// Create crawling routes with dependency injection
export const createCrawlingRoutes = (services: Services): readonly RouteConfig[] => [
  // Web sources management
  get("/api/crawl/sources", createGetWebSourcesHandler(services)),
  post("/api/crawl/sources", createPostWebSourceHandler(services)),
  put("/api/crawl/sources/([^/]+)", createPutWebSourceHandler(services)),
  del("/api/crawl/sources/([^/]+)", createDeleteWebSourceHandler(services)),

  // Crawl jobs management
  get("/api/crawl/jobs", createGetCrawlJobsHandler(services)),
  post("/api/crawl/jobs", createPostCrawlJobHandler(services)),
  get("/api/crawl/jobs/([^/]+)", createGetCrawlJobHandler(services)),
  post("/api/crawl/jobs/([^/]+)/process", createProcessJobHandler(services)),
  post("/api/crawl/process-all", createProcessAllJobsHandler(services)),

  // Crawling operations
  post("/api/crawl/start", createStartCrawlHandler(services)),
  post("/api/crawl/start-all", createStartAllCrawlsHandler(services)),
  post("/api/crawl/demo", createDemoCrawlHandler(services)),
];

// Web sources handlers
const createGetWebSourcesHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      const url = new URL(req.url);
      const active = url.searchParams.get("active");
      const country = url.searchParams.get("country");

      // Get web sources from shared module
      const sources = getWebSources({ active: active === "true", country });
      services.logger.info(`Retrieved ${sources.length} web sources`, {
        active: active === "true",
        country
      });

      return createJsonResponse({
        sources,
        count: sources.length,
      });
    } catch (error) {
      services.logger.error("Failed to get web sources", error as Error);
      return createErrorJsonResponse("Failed to retrieve web sources", 500);
    }
  };
};

const createPostWebSourceHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    const bodyResult = await parseJsonBody(req);

    if (!isOk(bodyResult)) {
      return createErrorJsonResponse("Invalid JSON body", 400);
    }

    const validation = validateWebSourceInput(bodyResult.data);

    return match(validation)
      .with({ success: true }, ({ data: validatedData }) => {
        const webSource: WebSource = {
          id: crypto.randomUUID(),
          ...validatedData,
          lastCrawled: undefined,
        };

        // TODO: Save to database
        services.logger.info(`Created web source: ${webSource.name}`, {
          webSourceId: webSource.id
        });

        return createJsonResponse(webSource, 201);
      })
      .with({ success: false }, ({ error }) => {
        services.logger.warn("Web source validation failed", { error });
        return createErrorJsonResponse(`Validation error: ${error.message}`, 400);
      })
      .exhaustive();
  };
};

const createPutWebSourceHandler = (services: Services) => {
  return async (req: Request, params: RouteParams): Promise<Response> => {
    try {
      const sourceId = params.param1;
      const body = await req.json();

      // TODO: Update web source in database
      services.logger.info(`Updated web source: ${sourceId}`);

      return createJsonResponse({ success: true });
    } catch (error) {
      services.logger.error("Failed to update web source", error as Error);
      return createErrorJsonResponse("Failed to update web source", 500);
    }
  };
};

const createDeleteWebSourceHandler = (services: Services) => {
  return async (req: Request, params: RouteParams): Promise<Response> => {
    try {
      const sourceId = params.param1;

      // TODO: Delete web source from database
      services.logger.info(`Deleted web source: ${sourceId}`);

      return createJsonResponse({ success: true });
    } catch (error) {
      services.logger.error("Failed to delete web source", error as Error);
      return createErrorJsonResponse("Failed to delete web source", 500);
    }
  };
};

// Crawl jobs handlers
const createGetCrawlJobsHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      const url = new URL(req.url);
      const status = url.searchParams.get("status");
      const limit = parseInt(url.searchParams.get("limit") || "20");

      // TODO: Get crawl jobs from database
      const jobs = getCrawlJobs({ status, limit });

      return createJsonResponse({
        jobs,
        count: jobs.length,
      });
    } catch (error) {
      services.logger.error("Failed to get crawl jobs", error as Error);
      return createErrorJsonResponse("Failed to retrieve crawl jobs", 500);
    }
  };
};

const createPostCrawlJobHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      const body = await req.json();
      const { webSourceId } = body;

      if (!webSourceId) {
        return createErrorJsonResponse("Web source ID is required", 400);
      }

      // TODO: Get web source and start crawl job
      const webSource = getWebSourceById(webSourceId);
      if (!webSource) {
        return createErrorJsonResponse("Web source not found", 404);
      }

      // TODO: Use actual crawling service
      const job: CrawlJob = {
        id: crypto.randomUUID(),
        webSourceId,
        status: "pending",
        startedAt: new Date(),
        pagesFound: 0,
        regulationsExtracted: 0,
        changesDetected: 0,
      };

      services.logger.info(`Created crawl job`, { jobId: job.id, webSourceId });

      return createJsonResponse(job, 201);
    } catch (error) {
      services.logger.error("Failed to create crawl job", error as Error);
      return createErrorJsonResponse("Failed to create crawl job", 500);
    }
  };
};

const createGetCrawlJobHandler = (services: Services) => {
  return async (_req: Request, params: RouteParams): Promise<Response> => {
    const jobId = params.param1;

    if (!services.crawling) {
      return createErrorJsonResponse("Crawling service not available", 503);
    }

    const jobResult = await services.crawling.getJobStatus(jobId);

    return match(jobResult)
      .with({ success: true }, ({ data: job }) => {
        services.logger.info(`Retrieved job status`, { jobId, status: job.status });
        return createJsonResponse(job);
      })
      .with({ success: false }, ({ error }) => {
        services.logger.error("Failed to get crawl job", error, { jobId });
        return createErrorJsonResponse(error.message, 404);
      })
      .exhaustive();
  };
};

const createProcessJobHandler = (services: Services) => {
  return async (_req: Request, params: RouteParams): Promise<Response> => {
    const jobId = params["param1"];

    if (!jobId) {
      return createErrorJsonResponse("Job ID is required", 400);
    }

    if (!services.crawling) {
      return createErrorJsonResponse("Crawling service not available", 503);
    }

    services.logger.info(`Processing crawl job: ${jobId}`);

    const processResult = await services.crawling.processCompletedJob(jobId);

    return match(processResult)
      .with({ success: true }, ({ data: result }) => {
        services.logger.info(`Job processed successfully`, {
          jobId,
          regulationsExtracted: result.regulations.length,
          changesDetected: result.changes.length
        });

        return createJsonResponse({
          message: "Job processed successfully",
          jobId,
          result: {
            job: result.job,
            regulationsExtracted: result.regulations.length,
            changesDetected: result.changes.length,
            regulations: result.regulations,
            changes: result.changes,
          },
        });
      })
      .with({ success: false }, ({ error }) => {
        services.logger.error("Failed to process crawl job", error, { jobId });
        return createErrorJsonResponse(error.message, 500);
      })
      .exhaustive();
  };
};

// Crawling operation handlers
const createStartCrawlHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      const body = await req.json();
      const { webSourceId } = body;

      if (!webSourceId) {
        return createErrorJsonResponse("Web source ID is required", 400);
      }

      // TODO: Start crawl using crawling service
      services.logger.info(`Starting crawl for web source: ${webSourceId}`);

      const job: CrawlJob = {
        id: crypto.randomUUID(),
        webSourceId,
        status: "running",
        startedAt: new Date(),
        pagesFound: 0,
        regulationsExtracted: 0,
        changesDetected: 0,
      };

      return createJsonResponse(job);
    } catch (error) {
      services.logger.error("Failed to start crawl", error as Error);
      return createErrorJsonResponse("Failed to start crawl", 500);
    }
  };
};

const createStartAllCrawlsHandler = (services: Services) => {
  return async (_req: Request, _params: RouteParams): Promise<Response> => {
    services.logger.info("Starting crawl for all active web sources");

    // Check if crawling service is available
    if (!services.crawling) {
      services.logger.warn("Crawling service not available - Firecrawl not configured");
      return createErrorJsonResponse(
        "Crawling service not available. Please configure FIRECRAWL_API_KEY environment variable.",
        503
      );
    }

    // Check if Firecrawl service is available
    if (!services.firecrawl) {
      services.logger.warn("Firecrawl service not available");
      return createErrorJsonResponse(
        "Firecrawl service not available. Please check your API key configuration.",
        503
      );
    }

    try {
      // Get all active web sources
      const sources = getWebSources({ active: true });

      if (sources.length === 0) {
        services.logger.info("No active web sources found");
        return createJsonResponse({
          message: "No active web sources to crawl",
          jobs: [],
          count: 0,
        });
      }

      // Start crawl jobs for each source using functional approach
      const jobResults = await Promise.all(
        sources.map(async (source) => {
          const jobResult = await services.crawling!.startCrawlJob(source);
          return { source, result: jobResult };
        })
      );

      // Separate successful and failed jobs using pattern matching
      const successfulJobs: CrawlJob[] = [];
      const failedJobs: Array<{ source: WebSource; error: Error }> = [];

      for (const { source, result } of jobResults) {
        match(result)
          .with({ success: true }, ({ data: job }) => {
            successfulJobs.push(job);
            services.logger.info(`Started crawl job for ${source.name}`, {
              jobId: job.id,
              webSourceId: source.id
            });
          })
          .with({ success: false }, ({ error }) => {
            failedJobs.push({ source, error });
            services.logger.error(`Failed to start crawl for ${source.name}`, error);
          })
          .exhaustive();
      }

      const responseMessage = failedJobs.length > 0
        ? `Started ${successfulJobs.length} crawl jobs, ${failedJobs.length} failed`
        : `Started ${successfulJobs.length} crawl jobs for active sources`;

      return createJsonResponse({
        message: responseMessage,
        jobs: successfulJobs,
        count: successfulJobs.length,
        failures: failedJobs.map(f => ({
          webSourceId: f.source.id,
          webSourceName: f.source.name,
          error: f.error.message,
        })),
      });
    } catch (error) {
      services.logger.error("Failed to start all crawls", error as Error);
      return createErrorJsonResponse("Failed to start crawls", 500);
    }
  };
};

const createDemoCrawlHandler = (services: Services) => {
  return async (_req: Request, _params: RouteParams): Promise<Response> => {
    services.logger.info("Running demo crawl with mock data");

    // Create a mock successful crawl job
    const mockJob = {
      id: crypto.randomUUID(),
      webSourceId: "demo-source",
      status: "completed" as const,
      firecrawlJobId: "demo-firecrawl-job",
      startedAt: new Date(Date.now() - 60000), // 1 minute ago
      completedAt: new Date(),
      pagesFound: 5,
      regulationsExtracted: 3,
      changesDetected: 1,
    };

    // Mock extracted regulations
    const mockRegulations = [
      {
        title: { en: "New Trade Regulation on Digital Services" },
        description: { en: "Updated regulations for digital service providers in international trade" },
        country_code: "US",
        source_agency: "Department of Commerce",
        category: "technical_regulation" as const,
        subcategory: "digital_services" as const,
        hs_codes: ["8523", "8524"],
        timeline: {
          effective_date: new Date("2024-01-01"),
        },
        impact_assessment: {
          economic: 8,
          operational: 6,
          compliance: 9,
          urgency: 7,
        },
        related_regulations: [],
        original_language: "en" as const,
        document_metadata: {
          source_url: "https://example.gov/regulations/digital-services",
          document_hash: crypto.randomUUID(),
          content_type: "text/html",
          file_size: 15420,
          extracted_at: new Date(),
        },
      },
      {
        title: { en: "Updated Tariff Schedule for Electronics" },
        description: { en: "Revised tariff rates for electronic components and devices" },
        country_code: "US",
        source_agency: "Department of Commerce",
        category: "tariff" as const,
        subcategory: "electronics" as const,
        hs_codes: ["8517", "8518", "8519"],
        timeline: {
          effective_date: new Date("2024-02-15"),
        },
        impact_assessment: {
          economic: 9,
          operational: 7,
          compliance: 8,
          urgency: 8,
        },
        related_regulations: [],
        original_language: "en" as const,
        document_metadata: {
          source_url: "https://example.gov/tariffs/electronics-2024",
          document_hash: crypto.randomUUID(),
          content_type: "text/html",
          file_size: 8750,
          extracted_at: new Date(),
        },
      },
    ];

    return createJsonResponse({
      message: "Demo crawl completed successfully",
      job: mockJob,
      regulations: mockRegulations,
      summary: {
        jobId: mockJob.id,
        status: mockJob.status,
        pagesFound: mockJob.pagesFound,
        regulationsExtracted: mockRegulations.length,
        processingTime: "60 seconds",
        categories: ["technical_regulation", "tariff"],
        countries: ["US"],
        agencies: ["Department of Commerce"],
      },
    });
  };
};

const createProcessAllJobsHandler = (services: Services) => {
  return async (_req: Request, _params: RouteParams): Promise<Response> => {
    if (!services.jobProcessor) {
      return createErrorJsonResponse("Job processor not available", 503);
    }

    services.logger.info("Manually triggering processing of all completed jobs");

    const result = await services.jobProcessor.processAllCompletedJobs();

    return match(result)
      .with({ success: true }, ({ data: processingResult }) => {
        services.logger.info("Manual job processing completed", processingResult);
        return createJsonResponse({
          message: "Job processing completed",
          result: processingResult,
        });
      })
      .with({ success: false }, ({ error }) => {
        services.logger.error("Manual job processing failed", error);
        return createErrorJsonResponse(error.message, 500);
      })
      .exhaustive();
  };
};

// Helper functions

const getCrawlJobs = (filters: { status?: string | null; limit: number }): CrawlJob[] => {
  // Mock data - replace with actual database query
  return [];
};

// getWebSourceById imported from web-sources.ts

const getCrawlJobById = (id: string): CrawlJob | null => {
  // Mock data - replace with actual database query
  return null;
};

// Validation types following functional patterns
type WebSourceInput = {
  readonly name: string;
  readonly url: string;
  readonly country: string;
  readonly agency: string;
  readonly category: readonly string[];
  readonly crawlConfig: {
    readonly includes?: readonly string[] | undefined;
    readonly excludes?: readonly string[] | undefined;
    readonly maxDepth: number;
    readonly limit: number;
  };
  readonly schedule: string;
  readonly active?: boolean;
};

type ValidationError = {
  readonly field: string;
  readonly message: string;
};

const validateWebSourceInput = (data: unknown): Result<WebSourceInput, ValidationError> => {
  if (typeof data !== "object" || data === null) {
    return { success: false, error: { field: "root", message: "Input must be an object" } };
  }

  const obj = data as Record<string, unknown>;

  if (!obj["name"] || typeof obj["name"] !== "string") {
    return { success: false, error: { field: "name", message: "Name is required and must be a string" } };
  }

  if (!obj["url"] || typeof obj["url"] !== "string") {
    return { success: false, error: { field: "url", message: "URL is required and must be a string" } };
  }

  if (!obj["country"] || typeof obj["country"] !== "string") {
    return { success: false, error: { field: "country", message: "Country is required and must be a string" } };
  }

  if (!obj["agency"] || typeof obj["agency"] !== "string") {
    return { success: false, error: { field: "agency", message: "Agency is required and must be a string" } };
  }

  if (!Array.isArray(obj["category"])) {
    return { success: false, error: { field: "category", message: "Category must be an array" } };
  }

  if (!obj["crawlConfig"] || typeof obj["crawlConfig"] !== "object") {
    return { success: false, error: { field: "crawlConfig", message: "Crawl config is required" } };
  }

  const crawlConfig = obj["crawlConfig"] as Record<string, unknown>;
  if (typeof crawlConfig["maxDepth"] !== "number" || typeof crawlConfig["limit"] !== "number") {
    return { success: false, error: { field: "crawlConfig", message: "maxDepth and limit must be numbers" } };
  }

  if (!obj["schedule"] || typeof obj["schedule"] !== "string") {
    return { success: false, error: { field: "schedule", message: "Schedule is required and must be a string" } };
  }

  return {
    success: true,
    data: {
      name: obj["name"] as string,
      url: obj["url"] as string,
      country: obj["country"] as string,
      agency: obj["agency"] as string,
      category: obj["category"] as readonly string[],
      crawlConfig: {
        includes: Array.isArray(crawlConfig["includes"]) ? crawlConfig["includes"] as readonly string[] : undefined,
        excludes: Array.isArray(crawlConfig["excludes"]) ? crawlConfig["excludes"] as readonly string[] : undefined,
        maxDepth: crawlConfig["maxDepth"] as number,
        limit: crawlConfig["limit"] as number,
      },
      schedule: obj["schedule"] as string,
      active: typeof obj["active"] === "boolean" ? obj["active"] : true,
    }
  };
};