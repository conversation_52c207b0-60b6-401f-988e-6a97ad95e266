// API routes using functional patterns and pattern matching
import { match } from "ts-pattern";
import { get, post, createJsonResponse, createErrorJsonResponse, parseJsonBody } from "../lib/router.ts";
import type { Services } from "./dependencies.ts";
import type { RouteConfig, RouteParams } from "../lib/router.ts";
import { isOk, isErr } from "../types/core.ts";

// Template imports for HTMX responses
import { 
  renderStatsTemplate, 
  renderChangesTemplate, 
  renderRegulationsTemplate, 
  renderChangesListTemplate 
} from "./templates.ts";

// Create API routes with dependency injection
export const createApiRoutes = (services: Services): readonly RouteConfig[] => [
  // Regulations endpoints
  get("/api/regulations", createGetRegulationsHandler(services)),
  get("/api/regulations/([^/]+)", createGetRegulationHandler(services)),
  get("/api/countries/([^/]+)/regulations", createGetRegulationsByCountryHandler(services)),
  
  // Changes endpoints  
  get("/api/changes", createGetChangesHandler(services)),
  
  // Dashboard endpoints
  get("/api/dashboard/stats", createGetDashboardStatsHandler(services)),
  
  // Subscription endpoints
  post("/api/subscriptions", createPostSubscriptionHandler(services)),
];

// Regulation handlers
const createGetRegulationsHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    const url = new URL(req.url);
    const country = url.searchParams.get("country");
    const category = url.searchParams.get("category");
    const priority = url.searchParams.get("priority");
    
    const filters = {
      ...(country && { country }),
      ...(category && { category }),
      ...(priority && { priority }),
      limit: parseInt(url.searchParams.get("limit") || "10"),
      offset: parseInt(url.searchParams.get("offset") || "0"),
    };

    const result = await services.regulation.findAll(filters);
    
    return match(result)
      .with({ success: true }, ({ data: regulations }) => {
        const isHTMX = req.headers.get("HX-Request") === "true";
        
        return match(isHTMX)
          .with(true, () => new Response(renderRegulationsTemplate(regulations), {
            headers: { "Content-Type": "text/html" },
          }))
          .with(false, () => createJsonResponse({
            regulations,
            pagination: {
              total: regulations.length,
              limit: filters.limit,
              offset: filters.offset,
              hasMore: regulations.length === filters.limit,
            },
          }))
          .exhaustive();
      })
      .with({ success: false }, ({ error }) => {
        const errorMessage = error instanceof Error ? error.message : String(error);
        return createErrorJsonResponse(errorMessage, 500);
      })
      .exhaustive();
  };
};

const createGetRegulationHandler = (services: Services) => {
  return async (_req: Request, params: RouteParams): Promise<Response> => {
    const id = params["param1"];
    if (!id) {
      return createErrorJsonResponse("Regulation ID is required", 400);
    }

    const regulationResult = await services.regulation.findById(id);
    if (!isOk(regulationResult)) {
      return createErrorJsonResponse("Regulation not found", 404);
    }

    const changesResult = await services.change.findByRegulation(id);
    const changes = isOk(changesResult) ? changesResult.data : [];

    return createJsonResponse({
      regulation: regulationResult.data,
      changes,
    });
  };
};

const createGetRegulationsByCountryHandler = (services: Services) => {
  return async (_req: Request, params: RouteParams): Promise<Response> => {
    const countryCode = params["param1"]?.toUpperCase();
    if (!countryCode) {
      return createErrorJsonResponse("Country code is required", 400);
    }

    const result = await services.regulation.findByCountry(countryCode);
    
    if (isErr(result)) {
      const errorMessage = result.error instanceof Error ? result.error.message : String(result.error);
      return createErrorJsonResponse(errorMessage, 500);
    }

    return createJsonResponse({
      country: countryCode,
      regulations: result.data,
      count: result.data.length,
    });
  };
};

// Change handlers
const createGetChangesHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    const url = new URL(req.url);
    const priority = url.searchParams.get("priority");
    const since = url.searchParams.get("since");
    
    const filters = {
      ...(priority && { priority }),
      ...(since && { since }),
      limit: parseInt(url.searchParams.get("limit") || "20"),
    };

    const result = await services.change.findAll(filters);
    
    return match(result)
      .with({ success: true }, ({ data: changes }) => {
        const isHTMX = req.headers.get("HX-Request") === "true";
        
        return match({ isHTMX, limit: filters.limit })
          .with({ isHTMX: true, limit: 5 }, () => 
            new Response(renderChangesTemplate(changes), {
              headers: { "Content-Type": "text/html" },
            })
          )
          .with({ isHTMX: true }, () =>
            new Response(renderChangesListTemplate(changes), {
              headers: { "Content-Type": "text/html" },
            })
          )
          .with({ isHTMX: false }, () =>
            createJsonResponse({
              changes,
              total: changes.length,
            })
          )
          .exhaustive();
      })
      .with({ success: false }, ({ error }) => {
        const errorMessage = error instanceof Error ? error.message : String(error);
        return createErrorJsonResponse(errorMessage, 500);
      })
      .exhaustive();
  };
};

// Dashboard handlers
const createGetDashboardStatsHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    const result = await services.stats.getDashboardStats();
    
    if (isErr(result)) {
      const errorMessage = result.error instanceof Error ? result.error.message : String(result.error);
      return createErrorJsonResponse(errorMessage, 500);
    }

    const data = result.data;
    const isHTMX = req.headers.get("HX-Request") === "true";
    
    return match(isHTMX)
      .with(true, () => new Response(renderStatsTemplate(data), {
        headers: { "Content-Type": "text/html" },
      }))
      .with(false, () => createJsonResponse(data))
      .exhaustive();
  };
};

// Subscription handlers
const createPostSubscriptionHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    const bodyResult = await parseJsonBody(req);
    
    if (!isOk(bodyResult)) {
      return createErrorJsonResponse("Invalid JSON body", 400);
    }

    const body = bodyResult.data as any;
    
    // Validate subscription data
    const subscriptionData = {
      user_id: body.user_id || "demo-user",
      name: body.name || "New Subscription",
      country_codes: body.country_codes || [],
      regulation_categories: body.regulation_categories || [],
      hs_codes: body.hs_codes || [],
      priority_threshold: body.priority_threshold || "medium",
      notification_config: {
        enabled: true,
        priority_threshold: body.priority_threshold || "medium",
        channels: body.channels || [],
        delay_minutes: 0,
        batch_notifications: false,
        custom_filters: [],
      },
      active: true,
    };

    const result = await services.subscription.create(subscriptionData);
    
    if (isErr(result)) {
      const errorMessage = result.error instanceof Error ? result.error.message : String(result.error);
      return createErrorJsonResponse(errorMessage, 500);
    }

    return createJsonResponse(result.data, 201);
  };
};

